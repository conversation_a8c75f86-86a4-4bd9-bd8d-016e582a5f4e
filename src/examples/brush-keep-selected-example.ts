/**
 * 笔刷保持选中状态功能示例
 *
 * 这个示例展示了如何使用 keepSelected 选项来控制笔刷是否保持目标元素的选中状态
 */

import { Canvas } from 'fabric';
import { PathBrush, RectBrush, LassoBrush } from '../render/base/package/brush';
import { IImage } from '../render/base/package/image/image';

// 示例1: 默认行为 - 笔刷激活时取消选中状态
export function createBrushWithDefaultBehavior(canvas: Canvas, targetElement: IImage) {
  const brush = new PathBrush(canvas, {
    shapeColor: '#53F6B4',
    shapeContainerName: 'example_mask_container',
    shapeContainerOpacity: 0.3,
    showShapeTitle: false,
    showShapeCloseBtn: false,
    targetElement: targetElement,
    erase: false,
    isMask: true,
    width: 30,
    // keepSelected 默认为 false，笔刷激活时会取消选中状态
  });

  return brush;
}

// 示例2: 保持选中状态 - 笔刷激活时保持目标元素选中
export function createBrushWithKeepSelected(canvas: Canvas, targetElement: IImage) {
  const brush = new PathBrush(canvas, {
    shapeColor: '#53F6B4',
    shapeContainerName: 'example_mask_container',
    shapeContainerOpacity: 0.3,
    showShapeTitle: false,
    showShapeCloseBtn: false,
    targetElement: targetElement,
    erase: false,
    isMask: true,
    width: 30,
    keepSelected: true, // 设置为 true，保持选中状态
  });

  return brush;
}

// 示例3: 矩形笔刷保持选中状态
export function createRectBrushWithKeepSelected(canvas: Canvas, targetElement: IImage) {
  const brush = new RectBrush(canvas, {
    shapeColor: '#FF6B6B',
    shapeContainerName: 'rect_mask_container',
    shapeContainerOpacity: 0.3,
    showShapeTitle: true,
    showShapeCloseBtn: true,
    targetElement: targetElement,
    erase: false,
    isMask: false,
    keepSelected: true, // 保持选中状态
  });

  return brush;
}

// 示例4: 套索笔刷保持选中状态
export function createLassoBrushWithKeepSelected(canvas: Canvas, targetElement: IImage) {
  const brush = new LassoBrush(canvas, {
    shapeColor: '#4ECDC4',
    shapeContainerName: 'lasso_mask_container',
    shapeContainerOpacity: 0.3,
    showShapeTitle: false,
    showShapeCloseBtn: false,
    targetElement: targetElement,
    erase: false,
    isMask: false,
    strokeDashArray: [5, 5],
    keepSelected: true, // 保持选中状态
  });

  return brush;
}

// 使用示例
export function exampleUsage() {
  // 假设你有一个 Canvas 实例和一个 IImage 实例
  const canvas = new Canvas('canvas-id');
  const targetImage = new IImage('image-url', {
    left: 100,
    top: 100,
    width: 200,
    height: 200,
  });

  canvas.add(targetImage);
  canvas.setActiveObject(targetImage);

  // 创建保持选中状态的笔刷
  const brush = createBrushWithKeepSelected(canvas, targetImage);

  // 激活笔刷 - 使用brush的setDrawingMode方法避免闪烁
  canvas.freeDrawingBrush = brush;
  brush.setDrawingMode(true);

  // 现在即使用户点击空白区域或进行其他操作，目标图像仍会保持选中状态
  // 这对于需要持续显示选中状态的场景非常有用

  // 清理笔刷
  function cleanupBrush() {
    brush.setDrawingMode(false);
    canvas.freeDrawingBrush = undefined;
    brush.destroy();
  }

  return { brush, cleanupBrush };
}

// 示例5: 测试直接设置isDrawingMode（现在也不会闪烁）
export function testDirectIsDrawingMode() {
  const canvas = new Canvas('canvas-id');
  const targetImage = new IImage('image-url', {
    left: 100,
    top: 100,
    width: 200,
    height: 200,
  });

  canvas.add(targetImage);
  canvas.setActiveObject(targetImage);

  // 创建保持选中状态的笔刷
  const brush = createBrushWithKeepSelected(canvas, targetImage);

  // 即使直接设置isDrawingMode，现在也不会闪烁
  canvas.freeDrawingBrush = brush;
  canvas.isDrawingMode = true; // 这样设置现在也是安全的

  // 清理
  function cleanup() {
    canvas.isDrawingMode = false;
    canvas.freeDrawingBrush = undefined;
    brush.destroy();
  }

  return { brush, cleanup };
}

/**
 * 功能说明：
 *
 * 1. keepSelected: false (默认)
 *    - 笔刷激活时，目标元素变为不可选中
 *    - 画布的选择功能被禁用
 *    - 当前选中的对象被取消选中
 *    - 笔刷销毁时，恢复选中功能和目标元素的可选中状态
 *
 * 2. keepSelected: true
 *    - 笔刷激活时，目标元素保持选中状态
 *    - 画布的选择功能保持启用
 *    - 目标元素无法被取消选中（通过重写onDeselect方法实现）
 *    - 笔刷销毁时，恢复目标元素的原始onDeselect方法
 *
 * 防闪烁机制：
 * - 重写Canvas的discardActiveObject方法，从根源阻止清除选中状态
 * - 重写目标元素的onDeselect方法，双重保护防止取消选中
 * - 使用brush.setDrawingMode()方法代替直接设置canvas.isDrawingMode
 * - 在设置绘制模式时使用标志位避免触发选中清除事件处理
 * - 使用多重保护机制：discardActiveObject阻止 + onDeselect阻止 + 事件监听恢复 + 标志位防重复
 *
 * 重要提示：
 * - 使用keepSelected: true时，推荐使用brush.setDrawingMode()方法
 * - 即使直接设置canvas.isDrawingMode，现在也不会导致闪烁（已从根源解决）
 *
 * 使用场景：
 * - keepSelected: false 适用于需要专注于绘制操作的场景
 * - keepSelected: true 适用于需要持续显示目标元素选中状态的场景，
 *   比如在绘制过程中需要显示元素的边界框或控制点，且不希望有闪烁效果
 */
