/**
 * Worker 使用示例
 * 
 * 这个示例展示了如何在项目中使用 worker 进行图像处理
 */

import workerpool from 'workerpool';

// 创建 worker pool
const pool = workerpool.pool('./worker/workers.iife.js');

/**
 * 示例1: 使用 worker 反转图像数据
 */
export async function invertImageDataExample(
  imageData: ImageData,
  rgb: [number, number, number] = [83, 246, 180]
) {
  try {
    const result = await pool.exec('imageDataInvertByMaskImage', [imageData, rgb]);
    return result as ImageData;
  } catch (error) {
    console.error('Worker error:', error);
    throw error;
  }
}

/**
 * 示例2: 检测图像是否有透明像素
 */
export async function checkTransparencyExample(imageData: ImageData) {
  try {
    const hasTransparent = await pool.exec('isImageDataHasTransparent', [imageData]);
    return hasTransparent as boolean;
  } catch (error) {
    console.error('Worker error:', error);
    throw error;
  }
}

/**
 * 示例3: 检测图像是否有 alpha 通道
 */
export async function checkAlphaChannelExample(imageData: ImageData) {
  try {
    const hasAlphaChannel = await pool.exec('hasAlpha', [imageData]);
    return hasAlphaChannel as boolean;
  } catch (error) {
    console.error('Worker error:', error);
    throw error;
  }
}

/**
 * 示例4: 超清替换 alpha 通道
 */
export async function upscaleReplaceAlphaExample(
  hdImage: ImageData,
  originImage: ImageData
) {
  try {
    const result = await pool.exec('upscaleReplaceAlpha', [hdImage, originImage]);
    return result as ImageData;
  } catch (error) {
    console.error('Worker error:', error);
    throw error;
  }
}

/**
 * 示例5: 使用遮罩抠图
 */
export async function cutoutWithMaskExample(
  initImage: ImageData,
  maskImage: ImageData
) {
  try {
    const result = await pool.exec('cutoutWithMask', [initImage, maskImage]);
    return result as ImageData;
  } catch (error) {
    console.error('Worker error:', error);
    throw error;
  }
}

/**
 * 批量处理示例
 */
export async function batchProcessExample(images: ImageData[]) {
  const results = await Promise.all(
    images.map(async (imageData, index) => {
      try {
        // 检查透明度
        const hasTransparent = await pool.exec('isImageDataHasTransparent', [imageData]);
        
        // 如果有透明像素，进行反转处理
        if (hasTransparent) {
          const inverted = await pool.exec('imageDataInvertByMaskImage', [
            imageData,
            [83, 246, 180]
          ]);
          return { index, processed: inverted, hasTransparent: true };
        }
        
        return { index, processed: imageData, hasTransparent: false };
      } catch (error) {
        console.error(`Error processing image ${index}:`, error);
        return { index, processed: imageData, hasTransparent: false, error };
      }
    })
  );
  
  return results;
}

/**
 * 清理 worker pool
 */
export function cleanupWorkerPool() {
  pool.terminate();
}

/**
 * 完整的图像处理流水线示例
 */
export class ImageProcessor {
  private pool: workerpool.WorkerPool;

  constructor(workerPath = './worker/workers.iife.js') {
    this.pool = workerpool.pool(workerPath);
  }

  async invertByMask(imageData: ImageData, rgb: [number, number, number]) {
    return this.pool.exec('imageDataInvertByMaskImage', [imageData, rgb]) as Promise<ImageData>;
  }

  async hasTransparent(imageData: ImageData) {
    return this.pool.exec('isImageDataHasTransparent', [imageData]) as Promise<boolean>;
  }

  async hasAlpha(imageData: ImageData) {
    return this.pool.exec('hasAlpha', [imageData]) as Promise<boolean>;
  }

  async upscaleReplaceAlpha(hdImage: ImageData, originImage: ImageData) {
    return this.pool.exec('upscaleReplaceAlpha', [hdImage, originImage]) as Promise<ImageData>;
  }

  async cutoutWithMask(initImage: ImageData, maskImage: ImageData) {
    return this.pool.exec('cutoutWithMask', [initImage, maskImage]) as Promise<ImageData>;
  }

  /**
   * 智能图像处理：根据图像特征选择合适的处理方式
   */
  async smartProcess(imageData: ImageData, options?: {
    maskColor?: [number, number, number];
    checkAlpha?: boolean;
  }) {
    const { maskColor = [83, 246, 180], checkAlpha = true } = options || {};

    // 检查是否有透明像素
    const hasTransparent = await this.hasTransparent(imageData);
    
    if (checkAlpha) {
      const hasAlphaChannel = await this.hasAlpha(imageData);
      console.log('Image has alpha channel:', hasAlphaChannel);
    }

    // 如果有透明像素，进行反转处理
    if (hasTransparent) {
      return this.invertByMask(imageData, maskColor);
    }

    return imageData;
  }

  destroy() {
    this.pool.terminate();
  }
}

/**
 * 使用示例
 */
export function exampleUsage() {
  // 创建处理器实例
  const processor = new ImageProcessor();

  // 示例：处理 Canvas 中的图像数据
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  
  if (ctx) {
    // 获取图像数据
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    
    // 智能处理
    processor.smartProcess(imageData, {
      maskColor: [255, 0, 0], // 红色遮罩
      checkAlpha: true
    }).then(processedData => {
      // 将处理后的数据放回 Canvas
      ctx.putImageData(processedData, 0, 0);
      console.log('Image processed successfully');
    }).catch(error => {
      console.error('Processing failed:', error);
    }).finally(() => {
      // 清理资源
      processor.destroy();
    });
  }
}

/**
 * 功能说明：
 * 
 * Worker 功能：
 * 1. imageDataInvertByMaskImage - 根据遮罩反转图像数据
 * 2. isImageDataHasTransparent - 检测是否有透明像素
 * 3. hasAlpha - 检测是否有 alpha 通道
 * 4. upscaleReplaceAlpha - 超清替换 alpha 通道
 * 5. cutoutWithMask - 使用遮罩抠图
 * 
 * 使用场景：
 * - 大量图像数据处理，避免阻塞主线程
 * - 复杂的像素级操作
 * - 批量图像处理
 * - 实时图像效果处理
 * 
 * 注意事项：
 * - Worker 处理是异步的，需要使用 Promise
 * - 记得在使用完毕后调用 terminate() 清理资源
 * - ImageData 在 Worker 间传递会被序列化，大图像可能影响性能
 */
