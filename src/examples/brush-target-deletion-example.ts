/**
 * 笔刷目标元素删除处理示例
 * 
 * 这个示例展示了当目标元素被删除时，笔刷如何自动处理并退出绘制模式
 */

import { Canvas } from 'fabric';
import { PathBrush } from '../render/base/package/brush';
import { IImage } from '../render/base/package/image/image';

// 示例1: 基本的目标元素删除处理
export function basicTargetDeletionHandling() {
  const canvas = new Canvas('canvas-id');
  const targetImage = new IImage('image-url', {
    left: 100,
    top: 100,
    width: 200,
    height: 200,
  });

  canvas.add(targetImage);
  canvas.setActiveObject(targetImage);

  // 创建笔刷
  const brush = new PathBrush(canvas, {
    shapeColor: '#53F6B4',
    shapeContainerName: 'example_mask_container',
    shapeContainerOpacity: 0.3,
    showShapeTitle: false,
    showShapeCloseBtn: false,
    targetElement: targetImage,
    erase: false,
    isMask: true,
    width: 30,
    keepSelected: true, // 保持选中状态
  });

  // 激活笔刷
  canvas.freeDrawingBrush = brush;
  brush.setDrawingMode(true);

  // 监听笔刷目标元素被删除的事件
  canvas.on('brush:target:removed', (e: any) => {
    console.log('笔刷的目标元素被删除，笔刷已自动销毁');
    console.log('被删除的目标元素:', e.targetElement);
    console.log('被销毁的笔刷:', e.brush);
    
    // 这里可以执行额外的清理工作
    // 比如通知用户、更新UI状态等
  });

  // 模拟删除目标元素的函数
  function deleteTargetElement() {
    // 删除目标元素
    canvas.remove(targetImage);
    
    // 笔刷会自动检测到目标元素被删除，并执行以下操作：
    // 1. 退出绘制模式 (canvas.isDrawingMode = false)
    // 2. 清除画布的freeDrawingBrush
    // 3. 清理笔刷资源
    // 4. 触发 'brush:target:removed' 事件
  }

  return { brush, deleteTargetElement };
}

// 示例2: 带有确认对话框的删除处理
export function deletionWithConfirmation() {
  const canvas = new Canvas('canvas-id');
  const targetImage = new IImage('image-url', {
    left: 100,
    top: 100,
    width: 200,
    height: 200,
  });

  canvas.add(targetImage);
  canvas.setActiveObject(targetImage);

  const brush = new PathBrush(canvas, {
    shapeColor: '#FF6B6B',
    shapeContainerName: 'example_mask_container',
    shapeContainerOpacity: 0.3,
    showShapeTitle: true,
    showShapeCloseBtn: true,
    targetElement: targetImage,
    erase: false,
    isMask: false,
    width: 20,
    keepSelected: true,
  });

  canvas.freeDrawingBrush = brush;
  brush.setDrawingMode(true);

  // 监听目标元素删除事件
  canvas.on('brush:target:removed', (e: any) => {
    // 显示通知
    alert('正在编辑的元素已被删除，笔刷模式已自动退出');
    
    // 可以在这里执行其他清理操作
    // 比如重置工具栏状态、清除相关UI等
  });

  // 安全删除函数（带确认）
  function safeDeleteTargetElement() {
    const confirmed = confirm('确定要删除正在编辑的元素吗？这将退出当前的笔刷模式。');
    if (confirmed) {
      canvas.remove(targetImage);
    }
  }

  return { brush, safeDeleteTargetElement };
}

// 示例3: 多个笔刷的删除处理
export function multiplebrushesDeletion() {
  const canvas = new Canvas('canvas-id');
  
  // 创建多个目标元素
  const targetImage1 = new IImage('image1-url', { left: 50, top: 50, width: 100, height: 100 });
  const targetImage2 = new IImage('image2-url', { left: 200, top: 50, width: 100, height: 100 });
  
  canvas.add(targetImage1, targetImage2);

  // 为每个元素创建笔刷
  const brush1 = new PathBrush(canvas, {
    shapeColor: '#53F6B4',
    shapeContainerName: 'container1',
    shapeContainerOpacity: 0.3,
    showShapeTitle: false,
    showShapeCloseBtn: false,
    targetElement: targetImage1,
    erase: false,
    isMask: true,
    width: 30,
    keepSelected: true,
  });

  const brush2 = new PathBrush(canvas, {
    shapeColor: '#4ECDC4',
    shapeContainerName: 'container2',
    shapeContainerOpacity: 0.3,
    showShapeTitle: false,
    showShapeCloseBtn: false,
    targetElement: targetImage2,
    erase: false,
    isMask: true,
    width: 30,
    keepSelected: true,
  });

  // 跟踪活动的笔刷
  let activeBrush: PathBrush | null = null;

  // 激活第一个笔刷
  function activateBrush1() {
    if (activeBrush) {
      activeBrush.setDrawingMode(false);
    }
    canvas.freeDrawingBrush = brush1;
    brush1.setDrawingMode(true);
    activeBrush = brush1;
  }

  // 激活第二个笔刷
  function activateBrush2() {
    if (activeBrush) {
      activeBrush.setDrawingMode(false);
    }
    canvas.freeDrawingBrush = brush2;
    brush2.setDrawingMode(true);
    activeBrush = brush2;
  }

  // 监听目标元素删除事件
  canvas.on('brush:target:removed', (e: any) => {
    const deletedBrush = e.brush;
    
    // 如果删除的是当前活动的笔刷，清除活动状态
    if (activeBrush === deletedBrush) {
      activeBrush = null;
    }
    
    console.log('笔刷已因目标元素删除而自动销毁:', deletedBrush);
  });

  // 删除所有元素
  function deleteAllElements() {
    canvas.remove(targetImage1, targetImage2);
    // 所有相关的笔刷都会自动销毁
    activeBrush = null;
  }

  return {
    brush1,
    brush2,
    activateBrush1,
    activateBrush2,
    deleteAllElements
  };
}

/**
 * 功能说明：
 * 
 * 自动删除处理机制：
 * 1. 监听 'object:removed' 事件
 * 2. 检查被删除的对象是否是笔刷的目标元素
 * 3. 如果是，自动执行以下操作：
 *    - 退出绘制模式 (canvas.isDrawingMode = false)
 *    - 清除画布的 freeDrawingBrush
 *    - 清理笔刷资源（遮罩、容器等）
 *    - 恢复画布的原始方法
 *    - 触发 'brush:target:removed' 事件
 * 
 * 与正常销毁的区别：
 * - 正常销毁：会恢复目标元素的位置和属性
 * - 删除时销毁：跳过目标元素相关操作（因为元素已被删除）
 * 
 * 事件处理：
 * - 'brush:target:removed' 事件会在目标元素删除时触发
 * - 事件对象包含 brush 和 targetElement 属性
 * - 可以在事件处理中执行额外的清理或通知操作
 * 
 * 最佳实践：
 * - 监听 'brush:target:removed' 事件来处理笔刷意外销毁
 * - 在删除元素前考虑是否需要用户确认
 * - 在多笔刷场景中正确管理活动状态
 */
