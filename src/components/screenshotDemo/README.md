# 图片截图工具 Demo

这是一个基于 Fabric.js 的图片截图工具，允许用户对指定的图片对象进行区域截取。用户可以通过拖拽矩形选择框来选择要截取的图片区域。

## 功能特性

### 🎯 核心功能
- **指定图片截图**: 针对选中的图片对象进行截图操作
- **矩形选择框**: 可拖拽、缩放的选择区域，限制在图片范围内
- **遮罩效果**: 突出显示选择区域，其他区域变暗
- **实时截图**: 根据选择区域导出高质量的图片裁剪结果
- **多种格式**: 支持 PNG、JPEG 等格式导出

### 🎨 交互功能
- **图片选择**: 必须先选择一个图片对象才能启动截图工具
- **拖拽移动**: 点击选择框中心拖拽移动位置（严格限制在图片范围内）
- **缩放调整**: 拖拽选择框边角调整大小（实时检查边界，防止超出图片）
- **平滑边界限制**:
  - 使用自定义控制处理器实现平滑的边界限制
  - 缩放时根据当前位置实时计算最大允许尺寸
  - 避免传统事件处理方式导致的闪烁问题
  - 移动和缩放操作都在transform过程中直接处理
- **最小尺寸限制**: 防止选择框过小
- **正方形模式**: 一键设置为正方形选择区域
- **重置功能**: 快速恢复默认大小和位置

### ⚙️ 配置选项
```typescript
interface ScreenshotOptions {
  target: IImage;                   // 目标图片对象（必需）
  renderMask: boolean;              // 是否显示遮罩
  renderMaskColor: string;          // 遮罩颜色
  renderMaskOpacity: number;        // 遮罩透明度
  selectionBoxColor: string;        // 选择框填充颜色
  selectionBoxOpacity: number;      // 选择框透明度
  selectionBoxStroke: string;       // 选择框边框颜色
  selectionBoxStrokeWidth: number;  // 选择框边框宽度
  initialWidth: number;             // 初始宽度
  initialHeight: number;            // 初始高度
  minWidth: number;                 // 最小宽度
  minHeight: number;                // 最小高度
}
```

## 使用方法

### 1. 选择目标图片
首先需要在画布上选择一个图片对象作为截图目标。

### 2. 启动截图工具
```typescript
import { RenderScreenshot } from '@/render/plugins/renderCut/plugin';

// 获取选中的图片对象
const activeObject = render._FC.getActiveObject();
if (activeObject instanceof IImage) {
  const screenshotTool = new RenderScreenshot(render, {
    target: activeObject,
    renderMask: true,
    renderMaskColor: '#000000',
    renderMaskOpacity: 0.6,
    selectionBoxColor: '#007bff',
    selectionBoxOpacity: 0.3,
    initialWidth: 300,
    initialHeight: 200,
  });
}
```

### 3. 获取选择区域信息
```typescript
const bounds = screenshotTool.getSelectionBounds();
console.log(bounds); // { left, top, width, height, centerX, centerY }
```

### 4. 执行截图
```typescript
// 截图会自动裁剪目标图片的选择区域
const blob = await screenshotTool.captureScreenshot({
  exportType: 'png',
  quality: 1,
  multiplier: 2,
});

// 下载截图
const url = URL.createObjectURL(blob);
const a = document.createElement('a');
a.href = url;
a.download = 'cropped-image.png';
a.click();
```

### 5. 调整选择框
```typescript
// 设置选择框位置和大小（会自动限制在图片范围内）
screenshotTool.setSelectionBox({
  left: 100,
  top: 100,
  width: 400,
  height: 300,
});
```

### 6. 销毁工具
```typescript
screenshotTool.__destroy__();
```

## API 参考

### 主要方法

| 方法 | 描述 | 参数 | 返回值 |
|------|------|------|--------|
| `getSelectionBounds()` | 获取选择区域边界信息 | - | `BoundsInfo` |
| `captureScreenshot(options?)` | 执行截图 | `ExportOptions` | `Promise<Blob>` |
| `setSelectionBox(options)` | 设置选择框属性 | `BoxOptions` | `void` |
| `__destroy__()` | 销毁工具实例 | - | `void` |

### 事件处理

工具会自动处理以下交互：
- 选择框的移动和缩放
- 最小尺寸限制
- 遮罩层的视口跟随
- 层级管理

## 使用流程

1. **选择图片**: 在画布上点击选择一个图片对象
2. **启动工具**: 点击"截图工具"按钮启动截图功能
3. **调整区域**: 拖拽选择框调整要截取的区域
4. **执行截图**: 点击"下载截图"获取裁剪后的图片
5. **退出工具**: 点击"退出截图"关闭工具

## 注意事项

1. **图片选择**: 必须先选择一个 IImage 类型的图片对象
2. **边界限制**: 选择框会自动限制在目标图片的边界内
3. **性能优化**: 大尺寸截图时建议适当调整 `multiplier` 参数
4. **浏览器兼容**: 需要支持 Canvas API 和 Blob API
5. **内存管理**: 使用完毕后及时调用 `__destroy__()` 方法
6. **层级管理**: 工具会自动管理选择框和遮罩的层级关系

## 示例代码

完整的使用示例请参考 `src/components/screenshotDemo/index.tsx` 文件。
