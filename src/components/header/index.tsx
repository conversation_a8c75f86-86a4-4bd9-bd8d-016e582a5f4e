import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Slider } from '@/components/ui/slider';
import { Frame, Type, LocateFixed, HardDriveDownload, SquareDashedK<PERSON>ban, ScanEye } from 'lucide-react';
import { useRenderStore, useUiStateStore } from '@/store';
import { cn } from '@/lib/utils';
import {
    CoreMouseMode,
    createImage,
    DeltaType,
    FabricObject,
    Group,
    IImage,
    IText,
    RenderExtend,
} from '@/render';
import Align from '../align/drapDown';
import { RenderCursor } from '@/render';
import { ElementName, getElementOptions } from '@/render/utils';
import Hand from '../hand';
import Loader from '../loader';
import FixText from '../fixText';
import ErrorStatus from '../error';
import ValidMask from '../validMask';
import Layer from '../layer';
import UploadImage from '../uploadImage';
import { useEffect, useState } from 'react';
import { showAllObjects } from '@/render/utils/canvas';
import UploadVideo from '../uploadVideo';
import FixTextComponent from '../fixtextComponent';
import ScreenshotDemo from '../screenshotDemo';

export default function Header() {
  const { render, renderSelection, historyPlugins, renderStyle } =
    useRenderStore();
  const {
    isCreateFrame,
    isCreateText,
    setIsCreateFrame,
    setIsCreateText,
    zoom,
  } = useUiStateStore();
  const [extendTarget, setExtendTarget] = useState<IImage | null>(null)
  const [extendContainer, setExtendContainer] = useState<RenderExtend | null>(null)

  const setCreateFrame = () => {
    if (isCreateText) {
      renderSelection?.setMode(CoreMouseMode.SELECTION);
      setIsCreateText(false);
      return;
    }
    if (isCreateFrame) {
      renderSelection?.setMode(CoreMouseMode.SELECTION);
      setIsCreateFrame(false);
      return;
    }
    renderSelection?.setMode(CoreMouseMode.FRAME);
    setIsCreateFrame(true);
    renderStyle?.setCursorStyle({
      move: RenderCursor.insertFrame,
      hover: RenderCursor.insertFrame,
      defaults: RenderCursor.insertFrame,
      mousedown: RenderCursor.insertFrame,
    });
  };
  const setCreateText = () => {
    if (isCreateFrame) {
      renderSelection?.setMode(CoreMouseMode.SELECTION);
      setIsCreateFrame(false);
      return;
    }
    if (isCreateText) {
      renderSelection?.setMode(CoreMouseMode.SELECTION);
      setIsCreateText(false);
      return;
    }
    renderSelection?.setMode(CoreMouseMode.TEXT);
    setIsCreateText(true);
    renderStyle?.setCursorStyle({
      move: RenderCursor.insertText,
      hover: RenderCursor.insertText,
      defaults: RenderCursor.insertText,
    });
  };

  const dbClickElement = (e: any) => {
    if (render?._FC) {
      showAllObjects(render._FC, [e.target], 0.01, 0, 0, 191, 191, 500)
    }
  }

  useEffect(() => {
    render?._FC.on('mouse:dblclick', dbClickElement)
    return () => {
      render?._FC.off('mouse:dblclick', dbClickElement)
    }
  }, [render?._FC])

  const replaceImage = async () => {
    const target = render?._FC.getActiveObject();
    if (!target) return;

    await render?.Actions.replaceImage(target as Group, {
      width: 512,
      height: 1024,
      scaleX: 1,
      scaleY: 1,
    }, 'lt');
  }
  const loadOriginImage = async () => {
    const el = await createImage('', {
      src: 'https://titan-h5.meitu.com/whee/assets/website/canvas-core/images/%E4%BC%81%E4%B8%9A%E5%BE%AE%E4%BF%A1%E6%88%AA%E5%9B%BE_3179d532-c96f-41a6-9f28-772d17b79714.png',
      width: 768,
      height: 1024,
    });
    //   const el = await createVideo('',{
    //       "source": "https://titan-h5.meitu.com/whee/assets/website/canvas-core/889b1a798e6a6501f51fca42faf59d31.mov",
    //       "cover": "https://titan-h5.meitu.com/whee/assets/website/canvas-core/images/%E4%BC%81%E4%B8%9A%E5%BE%AE%E4%BF%A1%E6%88%AA%E5%9B%BE_3179d532-c96f-41a6-9f28-772d17b79714.png",
    //       "frameType": "video",
    //       "_shape_name_": "Image_6",
    //       "_custom_data_history_": {
    //           "coverUrl": "https://roboneo-private.meitudata.com/mtopen/13808057a5c4472496dee7ec53ee70c9/95e27772-a1a6-4480-504c-8468c1101e5f/95e27772-a1a6-4480-504c-8468c1101e5f.gif?k=33e2e3149130f7ef6eb1cd0a19425ffc&t=6875297f",
    //           "uri": "https://roboneo-private.meitudata.com/mtopen/13808057a5c4472496dee7ec53ee70c9/95e27772-a1a6-4480-504c-8468c1101e5f/95e27772-a1a6-4480-504c-8468c1101e5f.gif?k=33e2e3149130f7ef6eb1cd0a19425ffc&t=6875297f",
    //           "url": "https://roboneo-private.meitudata.com/mtopen/13808057a5c4472496dee7ec53ee70c9/95e27772-a1a6-4480-504c-8468c1101e5f/95e27772-a1a6-4480-504c-8468c1101e5f.gif?k=33e2e3149130f7ef6eb1cd0a19425ffc&t=6875297f",
    //           "taskId": "3FE26F0178E34CDF97C446F1C64F2C6E"
    //       }
    //   })
    if (!render) return;
    // const { left, top } = collectViewportBoundsFromBottomLeft.call(render, {
    //   width: 768,
    //   height: 1024,
    //   left: 0,
    //   top: 0,
    // }, 0, 0, true)
    // el.set({
    //   left,
    //   top,
    // })
    // render?._FC.add(el);
    render?.addToViewPortCenterByArrangement(el);
    render?._FC.setActiveObject(el)
  };

  const replaceImageHandler = async () => {
    const target = render?._FC.getActiveObject();
    if (!target) return;
    replaceImage()
    // await render?.Actions.replaceImage(target as Group, {
    //   src: 'https://wheeai-pre.meitudata.com/static/67580b14499183540XdtcDf3VK6618.png?imageView2/2/w/350/h/466',
    //   // width: 350,
    //   // height: 466,
    // });
  };
  const backToOrigin = () => {
    render?.backToOriginPosition();
  };

  const undo = () => {
    historyPlugins?.undo();
  };

  const redo = () => {
    historyPlugins?.redo();
  };
  const getSceneData = () => {
    const data = render?.getSceneData() || [];
    localStorage.setItem('data', JSON.stringify(data));
  };
  const setSceneData = () => {
    const data = localStorage.getItem('data');
    if (data) {
      // render?.setSceneData(JSON.parse(data))
      render?.setSceneDataBySync(JSON.parse(data))
      // render?.setSceneData(JSON.parse('[{"_custom_data_":{},"_custom_data_history_":{},"_id_":"ElE0qEZv0Wj9GkNbsCoF5","_loading_":false,"_message_id_":"","_name_":"text","_new_prompt_":"","_old_prompt_":"","_parent_id_":"","_shape_name_":"我是一段文本","angle":0,"fill":"#fff","flipX":false,"flipY":false,"fontFamily":"Times New Roman","fontSize":120,"fontStyle":"normal","fontWeight":"normal","globalCompositeOperation":"source-over","height":135.59999999999997,"left":-1054.2502070309897,"letterSpacing":1200,"lineHeight":1.16,"linethrough":false,"opacity":1,"relativelyLeft":-1054.2502070309897,"relativelyTop":-607.2806697707169,"scaleX":1,"scaleY":1,"stroke":null,"strokeDashArray":null,"strokeLineCap":"butt","strokeLineJoin":"miter","strokeWidth":1,"text":"我是一段文本","textAlign":"left","top":-607.2806697707169,"underline":false,"visible":true,"width":1440,"zIndex":0},{"_custom_data_":{},"_custom_data_history_":{},"_id_":"MwLCsDg8MgAmBYCgZ1Dqx","_loading_":false,"_message_id_":"","_name_":"text","_new_prompt_":"","_old_prompt_":"","_parent_id_":"","_shape_name_":"我是一段文本","angle":0,"fill":"#fff","flipX":false,"flipY":false,"fontFamily":"Times New Roman","fontSize":120,"fontStyle":"normal","fontWeight":"normal","globalCompositeOperation":"source-over","height":135.59999999999997,"left":-848.2879064993194,"letterSpacing":1200,"lineHeight":1.16,"linethrough":false,"opacity":1,"relativelyLeft":-848.2879064993194,"relativelyTop":-1908.9275771051036,"scaleX":1,"scaleY":1,"stroke":null,"strokeDashArray":null,"strokeLineCap":"butt","strokeLineJoin":"miter","strokeWidth":1,"text":"我是一段文本","textAlign":"left","top":-1908.9275771051036,"underline":false,"visible":true,"width":1440,"zIndex":1}]'))
    }
  };
  const clear = () => {
    render?.clear();
  };

  const setFontSize = () => {
    // const modifyActiveObject = render?._FC.getActiveObject();
    // if (modifyActiveObject) {
    //   historyPlugins?.baseAction.getBeforeModifyOperation({
    //     target: modifyActiveObject,
    //   });
    //   modifyActiveObject.set('fontSize', 100);
    //   render?._FC.requestRenderAll();
    // }
    // const modifiedActiveObject = render?._FC.getActiveObject();
    // if (modifiedActiveObject) {
    //   const operation = historyPlugins?.baseAction.getModifiedOperation({
    //     target: modifiedActiveObject,
    //   });
    //   if (!operation) return;
    //   historyPlugins?.submit(operation);
    // }
  };

  const fixScreen = () => {
    render?.zoomToFitCanvas();
  };
  const addTemplate = () => {
    const data = localStorage.getItem('template');
    if (data ?? true) {
      render?.addFrame({
        "width": 768,
        "height": 1024,
        "backgroundColor": "#fff",
        "children": [
            {
                "fontFamily": "HeadlandScript",
                "fontSize": 46.41543,
                "fontWeight": "",
                "fontUrl": "https://material-center-pre.stariidata.com/material/font_subset/1745206135640.ttf",
                "text": "Whee Bakery",
                "left": -117.5416192350765,
                "top": -298.75583205,
                "angle": 0,
                "_name_": "text",
                "fill": "#000"
            },
            {
                "src": "https://wheeai.meitudata.com/static/68005f80e3e0fczw66s6c58998.png",
                "width": 468,
                "height": 406,
                "left": -122.68855000000002,
                "top": 14.936249999999973,
                "angle": 0,
                "_name_": "image"
            },
            {
                "fontFamily": "Roboto-Regular",
                "fontSize": 51.60662,
                "fontWeight": "",
                "fontUrl": "https://material-center-pre.stariidata.com/material/font_subset/1745206135409.ttf",
                "text": "new",
                "left": -326.71784918164064,
                "top": -158.90600970000003,
                "angle": 0,
                "_name_": "text",
                "fill": "#000"
            },
            {
                "fontFamily": "MAZIUSREVIEW20.09-Regular",
                "fontSize": 31.488695,
                "fontWeight": "",
                "fontUrl": "https://material-center-pre.stariidata.com/material/font_subset/1745206135641.otf",
                "text": " Take your pick from our list\nof all time favorite cake menu",
                "left": -133.2897454067443,
                "top": 303.04760337799996,
                "angle": 0,
                "_name_": "text",
                "fill": "#000"
            },
            {
                "fontFamily": "MAZIUSREVIEW20.09-Regular",
                "fontSize": 53.53389,
                "fontWeight": "",
                "fontUrl": "https://material-center-pre.stariidata.com/material/font_subset/1745206135641.otf",
                "text": "Tey now",
                "left": -137.16738823819523,
                "top": 401.26849785,
                "angle": 0,
                "_name_": "text",
                "fill": "#000"
            },
            {
                "fontFamily": "PlayfairDisplay-Regular",
                "fontSize": 31.488695,
                "fontWeight": "",
                "fontUrl": "https://material-center-pre.stariidata.com/material/font_subset/1745206135405.ttf",
                "text": "wheecakeee.com",
                "left": -109.97674532513452,
                "top": -447.68409932500003,
                "angle": 0,
                "_name_": "text",
                "fill": "#000"
            }
        ]
    }
    );
    }
  };
  const cacheTemplate = () => {
    const data = render?.getTemplateSceneData();
    localStorage.setItem('template', JSON.stringify(data));
  };
  return (
    <header className="border-b">
      <div className="flex h-14 items-center px-4 gap-4 overflow-x-auto no-scrollbar">
        <div className="flex items-center gap-1 border-x px-3">
          <Button
            variant="secondary"
            size="icon"
            onClick={() => {
              const els = render?._FC.getActiveObjects() as Array<Group | IText>;
              if (els.length === 0) return;
              render?.Actions.exportElementToBlob(
                els,
                {
                  includeType: [
                    ElementName.IMAGE,
                  ],
                  exportType: 'jpeg',
                  callBack: (data) => {
                    console.log(data)
                    return data
                  }
                }
              )
              // ).then(res => {
              //   URL.createObjectURL(res[0])
              //   const a = document.createElement('a')
              //   a.href = URL.createObjectURL(res[0])
              //   a.download = 'export.jpeg'
              //   a.click()
              // })
            }}
            className={cn(isCreateFrame && 'bg-gray-200', 'w-fit')}
          >
            导出所选元素
          </Button>
          <Button
            variant="secondary"
            size="icon"
            onClick={loadOriginImage}
            className={cn(isCreateFrame && 'bg-gray-200', 'w-fit')}
          >
            加载图片
          </Button>
          <Button
            variant="secondary"
            size="icon"
            onClick={replaceImageHandler}
            className={cn(isCreateFrame && 'bg-gray-200', 'w-fit')}
          >
            替换图片
          </Button>
          <UploadImage />
          <UploadVideo />
          <Button
            variant="secondary"
            size="icon"
            onClick={setCreateFrame}
            className={cn(isCreateFrame && 'bg-gray-200')}
          >
            <Frame className="h-5 w-5" />
          </Button>
          <Button
            variant="secondary"
            size="icon"
            onClick={setCreateText}
            className={cn(isCreateText && 'bg-gray-200')}
          >
            <Type className="h-5 w-5" />
          </Button>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-fit min-w-20">
                {zoom}%
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
              <Slider
                defaultValue={[100]}
                value={[zoom]}
                max={2000}
                min={10}
                step={1}
                className={cn('w-[90%]', 'mx-auto')}
                onValueChange={(value) => {
                  const zoom = value[0];
                  render?.setZoom(zoom / 100);
                }}
              />
            </PopoverContent>
          </Popover>
          <Button variant="secondary" size="icon" onClick={backToOrigin}>
            <LocateFixed className="h-5 w-5" />
          </Button>
          <Loader />
          <ErrorStatus />
          <Button variant="secondary" size="icon" onClick={undo}>
            撤销
          </Button>
          <Button variant="secondary" size="icon" onClick={redo}>
            重做
          </Button>
          <Hand />
          <Button
            variant="secondary"
            className="w-fit"
            size="icon"
            onClick={clear}
          >
            清除画布
          </Button>
          <Button
            variant="secondary"
            className="w-fit"
            size="icon"
            onClick={getSceneData}
          >
            <HardDriveDownload className="h-5 w-5" />
          </Button>
          <Button
            variant="secondary"
            className="w-fit"
            size="icon"
            onClick={setSceneData}
          >
            <SquareDashedKanban className="h-5 w-5" />
          </Button>

          <Button
            variant="secondary"
            className="w-fit"
            size="icon"
            onClick={addTemplate}
          >
            添加模版
          </Button>
          <Button
            variant="secondary"
            className="w-fit"
            size="icon"
            onClick={cacheTemplate}
          >
            缓存模版
          </Button>
          <FixText />
          <Button
            variant="secondary"
            className="w-fit"
            size="icon"
            onClick={setFontSize}
          >
            设置字体大小
          </Button>
          <Button
            variant="secondary"
            className="w-fit"
            size="icon"
            onClick={fixScreen}
          >
            <ScanEye className="h-5 w-5" />
          </Button>
          <Align />
          <ValidMask />
          <Layer />
          <Button
            variant="secondary"
            className="w-fit"
            size="icon"
            onClick={() => {
              const ob = render?._FC.getActiveObject();
              if (ob) {
                ob.set('visible', !ob.visible);
                render?._FC.requestRenderAll();
              }
            }}
          >
            隐藏
          </Button>
          <Button
            variant="secondary"
            className="w-fit"
            size="icon"
            onClick={() => {
              const target = render?._FC.getActiveObject();
              if (target && render) {
                const targetOptions = getElementOptions.call(render, target)
                render?.Actions.sendZIndex(target, 0);
                const afterOptions = getElementOptions.call(render, target)
                const operation = historyPlugins?.baseAction.getZIndexOperation([targetOptions], [afterOptions], DeltaType.CHANGE_Z_INDEX)
                if (operation) {
                  historyPlugins?.submit(operation)
                }
              }
            }}
          >
          设置层级
          </Button>
        </div>
        <div className="flex-1" />
        <ScreenshotDemo />
        <Button
            variant="secondary"
            className="w-fit"
            size="icon"
            onClick={() => {
              const target = render?._FC.getActiveObject();
              if (target && render) {
                const extend = new RenderExtend(render, {
                  target: target as IImage,
                  maxExtendWidth: target.getScaledWidth() + 700,
                  maxExtendHeight: target.getScaledHeight() + 700,
                  minExtendWidth: 100,
                  minExtendHeight: 100,
                  initExtendWidth: target.getScaledWidth() + 200,
                  initExtendHeight: target.getScaledHeight() + 200,
                  // allowImageOutOfExtend: false,
                  renderMask: true,
                  renderMaskColor: '#000',
                  renderMaskOpacity: 0.5,
                  initExtendLeft: target.getCenterPoint().x,
                  initExtendTop: target.getCenterPoint().y,
                  initExtendFill: 'red',
                  initExtendOpacity: 0.5,
                })
                setExtendTarget(target as IImage)
                setExtendContainer(extend)
              }
            }}
          >
            扩图
          </Button>
          <Button
            variant="secondary"
            className="w-fit"
            size="icon"
            onClick={() => {
              if (extendContainer && extendTarget) {
                extendContainer.setExtendContainerSizeAndPosition({
                  width: extendTarget.getScaledWidth() + 600,
                  height: extendTarget.getScaledHeight() + 200,
                  left: extendTarget.getCenterPoint().x,
                  top: extendTarget.getCenterPoint().y,
                })
              }
            }}
          >
            设置扩图大小
          </Button>
          <FixTextComponent />
      </div>
    </header>
  );
}
