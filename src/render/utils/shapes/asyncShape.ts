import { z } from 'zod/v4';
import { ShapePreset } from "../../base/package/preset"
import { FabricImage, FabricObject, FixedLayout, Group, GroupProps, ImageProps, IText, LayoutManager, TextProps } from "fabric";
import { ContainerElementOptions, ElementOptions, FrameElementOptions, IVideoOptionsSchema } from "../types";
import { ElementName } from "../enum";
import { Frame } from "../../base/package/frame/frame";
import { nanoid } from "nanoid";
import { ContainerProperties, ImageProperties, VideoProperties } from "../../base/ownDefaults";
import { IImage } from "../../base/package/image/image";
import { IVideo } from "../../base/package/video/video";
import { DefaultFontPreset } from "../../base/package/preset";
import { textEditingEntered, textEditingExited, textMouseDoubleClick } from "./textEvent";
import { Container } from "../../base/package/container/container";
import { loadFont } from '../loadfont';


/**
 * 创建Frame元素
 * @param parent 父级元素
 * @param params 画布参数
 * @returns Frame元素
 */
export const createFrame = (
    parentId: string,
    params: Partial<GroupProps> & Record<string, any>,
    children?: FabricObject[]
) => {
    children = children?.sort((a, b) => a._z_index_ - b._z_index_) ?? []
    const frame = new Frame(children, {
        ...params,
        _name_: ElementName.FRAME,
        labelText: params.labelText,
        showLabel: params.showLabel,
        _id_: params._id_ || nanoid(),
        interactive: true,
        subTargetCheck: true,
        _parent_id_: parentId,
        _loading_: false,
        fill: params.backgroundColor,
        _z_index_: params.zIndex || 0,
        _old_prompt_: params._old_prompt_,
        _new_prompt_: params._new_prompt_,
        _message_id_: params._message_id_,
    });
    frame.setControlVisible('mtl', false);
    frame.setControlVisible('mtr', false);
    frame.setControlVisible('mbl', false);
    frame.setControlVisible('mbr', false);
    return frame;
};

/**
 * 创建图片元素
 * @param parent 父级元素
 * @param params 图片参数
 * @returns 图片元素
 */
export const createImage = (
    parentId: string,
    params: Partial<ImageProps> & Record<string, any>
): Group => {
    const image = document.createElement('img');
    image.crossOrigin = 'anonymous';
    image.src = params.src

    const shape = new IImage({
        ...ImageProperties,
        ...params,
        _id_: params._id_ || nanoid(),
        _parent_id_: parentId,
        _shape_name_: params._shape_name_ || '',
        width: params.width || 100, // 设置默认尺寸
        height: params.height || 100,
        scaleX: params.scaleX || 1,
        scaleY: params.scaleY || 1,
        flipX: params.flipX || false,
        flipY: params.flipY || false,
        _z_index_: params.zIndex || 0,
        image,
        src: params.src,
        backgroundColor: ShapePreset.imageBackgroundColor
    });

    // 异步加载真实图片
    FabricImage.fromURL(params.src,{
        crossOrigin: 'anonymous'
    }).then((img: FabricImage) => {
        if (img.getElement()) {
            shape.fabricImage.setElement(img.getElement());
            shape.dirty = true;
            shape.loaded = true
            shape.canvas?.fire("object:loaded", { target: shape })
            shape.backgroundColor = "transparent"
            shape.setCoords();
            if (!params.width) shape.set('width', img.width);
            if (!params.height) shape.set('height', img.height);
            const canvas = shape.canvas || shape.group?.canvas;
            if (canvas) {
                canvas.renderAll();
            }
        }
    },);

    return shape;
};

/**
 * 创建图片元素
 * @param parent 父级元素
 * @param params 图片参数
 * @returns 图片元素
 */
export const createVideo = (
    parentId: string,
    params: Partial<Omit<ImageProps, "src">> & Record<string, any> & { source: string, cover: string }
) => {
    const image = document.createElement('img');
    image.crossOrigin = 'anonymous';
    image.src = params.cover

    const shape = new IVideo({
        ...VideoProperties,
        ...params,
        _id_: params._id_ || nanoid(),
        _shape_name_: params._shape_name_ || '',
        _parent_id_: parentId,
        width: params.width || 0,
        height: params.height || 0,
        scaleX: params.scaleX || 1,
        scaleY: params.scaleY || 1,
        flipX: params.flipX || false,
        flipY: params.flipY || false,
        _z_index_: params.zIndex || 0,
        cover: image,
        source: params.source,
        backgroundColor: ShapePreset.videoBackgroundColor
    })
// 异步加载真实图片
    FabricImage.fromURL(params.cover,{
        crossOrigin: 'anonymous'
    }).then((img: FabricImage) => {
        if (img.getElement()) {
            shape.fabricImage.setElement(img.getElement());
            shape.dirty = true;
            shape.loaded = true
            shape.canvas?.fire("object:loaded", { target: shape })
            shape.setCoords();
            shape.backgroundColor = "transparent"
            if (!params.width) shape.set('width', img.width);
            if (!params.height) shape.set('height', img.height);
            const canvas = shape.canvas || shape.group?.canvas;
            if (canvas) {
                canvas.renderAll();
            }
        }
    },);
    return shape
}


/**
 * 创建文本元素
 * @param parent 父级元素
 * @param params 文本参数
 * @returns 文本元素
 */
export const createText = (
    parentId: string,
    params: Partial<TextProps> & Record<string, any>
) => {

    const text = new IText(params.text);
    text.set({
        ...DefaultFontPreset,
        ...params,
        flipX: false,
        flipY: false,
        charSpacing: params.letterSpacing || 0,
        _parent_id_: parentId,
        _id_: params._id_ || nanoid(),
        _loading_: false,
        editable: true,
        _shape_name_: params.text,
        _name_: ElementName.TEXT,
        _font_url_: params._font_url_ || params.fontUrl,
        scaleX: params.scaleX || 1,
        scaleY: params.scaleY || 1,
        _z_index_: params.zIndex || 0,
        _old_prompt_: '',
        _new_prompt_: '',
        _message_id_: '',
    });
    // if (params.flipX !== void 0) {
    //   text.set('flipX', params.flipX)
    // }
    // if (params.flipY !== void 0) {
    //   text.set('flipY', params.flipY)
    // }
    text.on('editing:entered', textEditingEntered.bind(text))
    text.on('editing:exited', textEditingExited.bind(text))
    text.on('mousedblclick', textMouseDoubleClick.bind(text))
    text.set("loaded", false)
    loadFont([{
        fontFamily: params.fontFamily || '',
        fontUrl: params._font_url_ || params.fontUrl,
    }]).then(() => {
        if (text.canvas) {
            text.set("loaded", true)
            text.canvas.renderAll()
            text.canvas.fire('object:loaded', { target: text })
        }
    })
    return text;
};


/**
 * 组合元素
 * @param {FabricObject} element
 */
export const createContainerElements = (
    parentId: string,
    elements: FabricObject[],
    params?: Partial<GroupProps> & Record<string, any>
) => {
    // 计算元素的坐标
    const left = Math.min(
        ...elements.map((element) => element.getBoundingRect().left)
    );
    const top = Math.min(
        ...elements.map((element) => element.getBoundingRect().top)
    );
    const width =
        Math.max(
            ...elements.map(
                (element) =>
                    element.getBoundingRect().left + element.getBoundingRect().width
            )
        ) - left;
    const height =
        Math.max(
            ...elements.map(
                (element) =>
                    element.getBoundingRect().top + element.getBoundingRect().height
            )
        ) - top;
    const options = { ...ContainerProperties, ...params };
    elements.sort((a, b) => a._z_index_ - b._z_index_);
    const group = new Container(elements, {
        ...options,
        _id_: params?._id_ || nanoid(),
        _parent_id_: parentId,
        width: params?.width || width,
        height: params?.height || height,
        left: params?.left || left + (params?.width || width) / 2,
        top: params?.top || top + (params?.height || height) / 2,
        angle: params?.angle || 0,
        flipX: params?.flipX || false,
        flipY: params?.flipY || false,
        scaleX: params?.scaleX || 1,
        scaleY: params?.scaleY || 1,
        _shape_name_: params?._shape_name_ || '',
        _z_index_: params?.zIndex || 0,
        layoutManager: new LayoutManager(new FixedLayout()),
    });

    return group;
};

export const createElementBySync = (
    options: ElementOptions
): Group | IText => {
    if (options._name_ === ElementName.IMAGE)
        return createImage(options._parent_id_ ?? '', options);
    if (options._name_ === ElementName.TEXT)
        return createText(options._parent_id_ ?? '', options);
    if (options._name_ === ElementName.VIDEO)
        return createVideo(options._parent_id_ ?? '', options as z.infer<typeof IVideoOptionsSchema>);
    if (options._name_ === ElementName.FRAME) {
        const children = (options as FrameElementOptions).children ?? [];
        const els = children
            .map((child) => createElementBySync(child));
        const f = createFrame(options._parent_id_ ?? '', options)
        f.add(...els)
        return f

    }
    if (options._name_ === ElementName.CONTAINER) {
        const children = (options as ContainerElementOptions).children ?? [];
        const els = children
            .map(item => ({
                ...item,
            }))
            .map((child) => createElementBySync(child));
        const c = createContainerElements(
            options._parent_id_ ?? '',
            [],
            options
        );
        c.add(...els)
        return c
    }

    throw new Error('未知的元素类型');
};
