# 笔刷保持选中状态和目标元素删除处理功能

## 功能概述

为笔刷系统添加了两个重要功能：
1. `keepSelected` 选项：控制笔刷激活时是否保持目标元素的选中状态
2. 自动删除处理：当目标元素被删除时，笔刷自动销毁并退出绘制模式

## 修改的文件

1. **src/render/base/package/brush/types.ts** - 添加 `keepSelected` 选项类型定义
2. **src/render/base/package/brush/base/baseBrush.ts** - 实现核心逻辑
3. **src/examples/brush-target-deletion-example.ts** - 使用示例

## 核心实现

### 1. 类型定义

```typescript
export interface BaseBrushOptions {
  // ... 其他选项
  keepSelected?: boolean; // 是否保持选中状态，默认为false
}
```

### 2. 保持选中状态功能

#### 构造函数逻辑
```typescript
if (!this.options.keepSelected) {
  this.options.targetElement.set("selectable", false);
  this.canvas.set('selection', false)
  this.canvas.discardActiveObject();
} else {
  // 保持选中状态并重写相关方法
  this.canvas.setActiveObject(this.options.targetElement);
  this.overrideTargetElementDeselect();
  this.overrideCanvasDiscardActiveObject();
}
```

#### 防闪烁机制
采用多重保护机制彻底解决闪烁问题：

**方案1：重写Canvas的discardActiveObject方法（核心）**
```typescript
private overrideCanvasDiscardActiveObject = () => {
  this.originalDiscardActiveObject = this.canvas.discardActiveObject.bind(this.canvas);
  
  (this.canvas as any).discardActiveObject = (e?: any) => {
    if (this.options.keepSelected) {
      const activeObject = this.canvas.getActiveObject();
      if (activeObject === this.options.targetElement) {
        return this.canvas; // 阻止清除选中状态
      }
    }
    return this.originalDiscardActiveObject ? this.originalDiscardActiveObject(e) : this.canvas;
  };
}
```

**方案2：重写目标元素的onDeselect方法（辅助）**
```typescript
private overrideTargetElementDeselect = () => {
  this.originalOnDeselect = (this.options.targetElement as any).onDeselect;
  
  (this.options.targetElement as any).onDeselect = () => {
    if (this.options.keepSelected) {
      return false; // 阻止取消选中
    }
    return this.originalOnDeselect ? this.originalOnDeselect.call(this.options.targetElement) : true;
  };
}
```

### 3. 目标元素删除处理

#### 事件监听
```typescript
bindEvent = () => {
  this.canvas.on('object:added', this.objectAddedHandler)
  this.canvas.on('object:removed', this.objectRemovedHandler) // 新增
  // ... 其他事件
}
```

#### 删除检测
```typescript
objectRemovedHandler = (e: any) => {
  const removedObject = e.target;
  if (removedObject === this.options.targetElement) {
    this.handleTargetElementRemoved();
  }
}
```

#### 自动销毁处理
```typescript
private handleTargetElementRemoved = () => {
  // 退出绘制模式
  this.canvas.isDrawingMode = false;
  this.canvas.freeDrawingBrush = undefined;
  
  // 清理笔刷资源
  this.cleanupBrushResources();
  
  // 触发自定义事件
  this.canvas.fire('brush:target:removed', {
    brush: this,
    targetElement: this.options.targetElement
  });
}
```

## 使用方法

### 基本用法

```typescript
// 创建保持选中状态的笔刷
const brush = new PathBrush(canvas, {
  // ... 其他选项
  keepSelected: true, // 保持选中状态，无闪烁
});

// 激活笔刷
canvas.freeDrawingBrush = brush;
brush.setDrawingMode(true); // 推荐使用，但直接设置 canvas.isDrawingMode 也可以

// 监听目标元素删除事件
canvas.on('brush:target:removed', (e: any) => {
  console.log('笔刷已因目标元素删除而自动销毁');
  // 执行额外的清理或通知操作
});
```

### 安全删除目标元素

```typescript
function safeDeleteTargetElement() {
  const confirmed = confirm('确定要删除正在编辑的元素吗？这将退出当前的笔刷模式。');
  if (confirmed) {
    canvas.remove(targetElement);
    // 笔刷会自动销毁并退出绘制模式
  }
}
```

## 技术特点

### 保持选中状态功能
1. **无闪烁**: 从根源阻止 `discardActiveObject` 调用
2. **多重保护**: Canvas方法重写 + 元素方法重写 + 事件监听
3. **向后兼容**: 默认行为不变，新功能为可选
4. **内存安全**: 正确保存和恢复原始方法

### 删除处理功能
1. **自动检测**: 监听 `object:removed` 事件
2. **智能清理**: 区分正常销毁和删除销毁
3. **事件通知**: 触发 `brush:target:removed` 事件
4. **完整清理**: 自动退出绘制模式并清理资源

## 适用场景

### keepSelected: false（默认）
- 需要专注于绘制操作的场景
- 传统的笔刷使用方式

### keepSelected: true
- 需要持续显示目标元素选中状态的场景
- 在绘制过程中显示元素边界框
- 保持控制点可见
- 避免用户误操作取消选中
- 提供更好的用户体验

### 删除处理
- 防止因元素删除导致的笔刷状态异常
- 自动清理避免内存泄漏
- 提供删除通知机制

## 最佳实践

1. **监听删除事件**: 始终监听 `brush:target:removed` 事件
2. **用户确认**: 在删除元素前考虑是否需要用户确认
3. **状态管理**: 在多笔刷场景中正确管理活动状态
4. **资源清理**: 确保在组件卸载时正确清理笔刷
5. **错误处理**: 处理目标元素意外删除的情况

## 注意事项

1. 使用 `keepSelected: true` 时，目标元素将无法通过常规方式取消选中
2. 必须调用 `brush.destroy()` 来正确清理资源和恢复原始行为
3. 目标元素删除时会自动清理，无需手动调用 `destroy()`
4. 该功能与现有的笔刷功能完全兼容，不影响绘制操作
5. 建议监听 `brush:target:removed` 事件来处理笔刷意外销毁的情况
