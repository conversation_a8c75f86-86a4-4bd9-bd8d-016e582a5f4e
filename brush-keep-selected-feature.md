# 笔刷保持选中状态功能实现

## 功能概述

为笔刷系统添加了 `keepSelected` 选项，用于控制笔刷激活时是否保持目标元素的选中状态。

## 修改的文件

1. **src/render/base/package/brush/types.ts** - 添加 `keepSelected` 选项类型定义
2. **src/render/base/package/brush/base/baseBrush.ts** - 实现核心逻辑
3. **src/examples/brush-keep-selected-example.ts** - 使用示例

## 核心实现

### 1. 类型定义

```typescript
export interface BaseBrushOptions {
  // ... 其他选项
  keepSelected?: boolean; // 是否保持选中状态，默认为false
}
```

### 2. 构造函数逻辑

```typescript
// 根据keepSelected选项决定是否取消选中状态
if (!this.options.keepSelected) {
  this.options.targetElement.set("selectable", false);
  this.canvas.set('selection', false)
  this.canvas.discardActiveObject();
} else {
  // 如果keepSelected为true，确保目标元素保持选中状态
  this.canvas.setActiveObject(this.options.targetElement);
  // 重写目标元素的onDeselect方法，防止被取消选中
  this.overrideTargetElementDeselect();
}
```

### 3. 防闪烁机制

#### 问题分析
闪烁问题的根本原因是 Fabric.js 的多层内置行为：
1. 当设置 `canvas.isDrawingMode = true` 时，Fabric.js 会自动调用 `discardActiveObject()`
2. 在笔刷的 `onMouseDown` 时，PencilBrush 也会调用 `canvas.discardActiveObject()`
3. 这些调用都会触发 `selection:cleared` 事件，导致外部监听器响应
4. 如果我们在事件处理中重新选中元素，会造成视觉闪烁
5. 关键问题：**Fabric.js 在多个地方都会主动清除选中状态**

#### 解决方案

采用多重保护机制：

**方案1：提供安全的setDrawingMode方法（核心方案）**
```typescript
public setDrawingMode = (isDrawingMode: boolean) => {
  if (this.options.keepSelected) {
    this.isSettingDrawingMode = true;
    this.canvas.isDrawingMode = isDrawingMode;

    if (isDrawingMode) {
      // 在设置绘制模式后立即恢复选中状态
      setTimeout(() => {
        this.canvas.setActiveObject(this.options.targetElement);
        this.canvas.renderAll();
        this.isSettingDrawingMode = false;
      }, 0);
    } else {
      this.isSettingDrawingMode = false;
    }
  } else {
    this.canvas.isDrawingMode = isDrawingMode;
  }
}
```

**方案2：重写Canvas的discardActiveObject方法（核心方案）**
```typescript
private overrideCanvasDiscardActiveObject = () => {
  // 保存原始的discardActiveObject方法
  this.originalDiscardActiveObject = this.canvas.discardActiveObject.bind(this.canvas);

  // 重写discardActiveObject方法
  this.canvas.discardActiveObject = () => {
    if (this.options.keepSelected) {
      const activeObject = this.canvas.getActiveObject();
      // 如果当前选中的是目标元素，则不执行discardActiveObject
      if (activeObject === this.options.targetElement) {
        return this.canvas;
      }
    }
    // 否则调用原始的discardActiveObject方法
    return this.originalDiscardActiveObject ? this.originalDiscardActiveObject() : this.canvas;
  };
}
```

**方案3：重写onDeselect方法（辅助方案）**
```typescript
private overrideTargetElementDeselect = () => {
  // 保存原始的onDeselect方法
  this.originalOnDeselect = (this.options.targetElement as any).onDeselect;

  // 重写onDeselect方法
  (this.options.targetElement as any).onDeselect = () => {
    // 在keepSelected模式下，阻止目标元素被取消选中
    if (this.options.keepSelected) {
      return false;
    }
    return this.originalOnDeselect ? this.originalOnDeselect.call(this.options.targetElement) : true;
  };
}
```

**方案2：事件监听恢复（备用方案）**
```typescript
selectionClearedHandler = (e: any) => {
  if (this.options.keepSelected && !this.isReselecting) {
    const deselectedObjects = e.deselected || [];
    const isTargetDeselected = deselectedObjects.some((obj: FabricObject) => obj === this.options.targetElement);
    
    if (isTargetDeselected) {
      this.isReselecting = true;
      requestAnimationFrame(() => {
        this.canvas.setActiveObject(this.options.targetElement);
        this.canvas.renderAll();
        this.isReselecting = false;
      });
    }
  }
}
```

**方案4：事件监听恢复（备用方案）**
```typescript
selectionClearedHandler = (e: any) => {
  if (this.options.keepSelected && !this.isReselecting && !this.isSettingDrawingMode) {
    const deselectedObjects = e.deselected || [];
    const isTargetDeselected = deselectedObjects.some((obj: FabricObject) => obj === this.options.targetElement);

    if (isTargetDeselected) {
      this.isReselecting = true;
      requestAnimationFrame(() => {
        this.canvas.setActiveObject(this.options.targetElement);
        this.canvas.renderAll();
        this.isReselecting = false;
      });
    }
  }
}
```

**方案5：标志位防重复**
```typescript
private isReselecting: boolean = false; // 防止重复选中造成的闪烁
private isSettingDrawingMode: boolean = false; // 防止设置绘制模式时的干扰
```

### 4. 清理机制

```typescript
destroy = () => {
  if (!this.options.keepSelected) {
    this.options.targetElement.set("selectable", true);
    this.canvas.set('selection', true)
  } else {
    // 恢复目标元素的原始onDeselect方法
    this.restoreTargetElementDeselect();
  }
  // ... 其他清理逻辑
}
```

## 使用方法

### 默认行为（keepSelected: false）
```typescript
const brush = new PathBrush(canvas, {
  // ... 其他选项
  // keepSelected 默认为 false，笔刷激活时会取消选中状态
});
```

### 保持选中状态（keepSelected: true）
```typescript
const brush = new PathBrush(canvas, {
  // ... 其他选项
  keepSelected: true, // 保持选中状态，无闪烁
});
```

## 技术特点

1. **无闪烁**: 通过重写onDeselect方法从源头阻止取消选中操作
2. **多重保护**: 结合事件监听和标志位，确保功能稳定
3. **向后兼容**: 默认行为不变，新功能为可选
4. **内存安全**: 正确保存和恢复原始方法，避免内存泄漏
5. **事件清理**: 完整的事件绑定和解绑机制

## 适用场景

- **keepSelected: false**: 需要专注于绘制操作的场景
- **keepSelected: true**: 需要持续显示目标元素选中状态的场景，如：
  - 在绘制过程中显示元素边界框
  - 保持控制点可见
  - 避免用户误操作取消选中
  - 提供更好的用户体验

## 注意事项

1. 使用 `keepSelected: true` 时，目标元素将无法通过常规方式取消选中
2. 必须调用 `brush.destroy()` 来正确清理资源和恢复原始行为
3. 该功能与现有的笔刷功能完全兼容，不影响绘制操作
