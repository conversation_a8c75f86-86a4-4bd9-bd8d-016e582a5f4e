{
  "lockfileVersion": 1,
  "workspaces": {
    "": {
      "name": "@meitu/whee-infinite-canvas",
      "dependencies": {
        "@arco-design/web-react": "^2.66.1",
        "@meitu/upload": "^4.8.5",
        "@radix-ui/react-avatar": "^1.1.2",
        "@radix-ui/react-dropdown-menu": "^2.1.4",
        "@radix-ui/react-popover": "^1.1.4",
        "@radix-ui/react-scroll-area": "^1.2.2",
        "@radix-ui/react-slider": "^1.2.2",
        "@radix-ui/react-slot": "^1.1.1",
        "@radix-ui/react-toast": "^1.2.4",
        "@radix-ui/react-tooltip": "^1.1.8",
        "@tanstack/react-query": "^5.80.6",
        "@velipso/polybool": "^2.0.11",
        "await-to-js": "^3.0.0",
        "axios": "^1.9.0",
        "canvaskit-wasm": "^0.39.1",
        "class-variance-authority": "^0.7.1",
        "clsx": "^2.1.1",
        "events": "^3.3.0",
        "fabric": "^6.0.0",
        "hotkeys-js": "^3.13.9",
        "jss": "^10.10.0",
        "lodash-es": "^4.17.21",
        "lottie-web": "^5.12.2",
        "lucide-react": "^0.469.0",
        "nanoid": "^5.0.9",
        "primereact": "^10.9.5",
        "react": "^18.0.0",
        "react-dom": "^18.0.0",
        "reset.css": "^2.0.2",
        "svg64": "^2.0.0",
        "swr": "^2.3.3",
        "tailwind-merge": "^2.6.0",
        "tailwindcss-animate": "^1.0.7",
        "vite-plugin-dts": "^4.4.0",
        "workerpool": "^9.2.0",
        "zod": "^3.25.20",
        "zustand": "^5.0.2",
      },
      "devDependencies": {
        "@eslint/js": "^9.17.0",
        "@types/events": "^3.0.3",
        "@types/lodash-es": "^4.17.12",
        "@types/node": "^22.10.2",
        "@types/react": "^18.3.18",
        "@types/react-dom": "^18.3.5",
        "@vitejs/plugin-react-swc": "^3.5.0",
        "autoprefixer": "^10.4.20",
        "eslint": "^9.17.0",
        "eslint-plugin-react-hooks": "^5.0.0",
        "eslint-plugin-react-refresh": "^0.4.16",
        "globals": "^15.14.0",
        "i": "^0.3.7",
        "postcss": "^8.4.49",
        "tailwindcss": "^3.4.17",
        "typescript": "~5.6.2",
        "typescript-eslint": "^8.18.2",
        "vite": "npm:rolldown-vite@latest",
      },
      "peerDependencies": {
        "fabric": "^6.0.0",
        "react": "^18.0.0",
        "react-dom": "^18.0.0",
      },
    },
  },
  "packages": {
    "@alloc/quick-lru": ["@alloc/quick-lru@5.2.0", "http://npm.meitu-int.com/@alloc%2fquick-lru/-/quick-lru-5.2.0.tgz", {}, ""],

    "@arco-design/color": ["@arco-design/color@0.4.0", "http://npm.meitu-int.com/@arco-design/color/-/color-0.4.0.tgz", { "dependencies": { "color": "^3.1.3" } }, ""],

    "@arco-design/web-react": ["@arco-design/web-react@2.66.1", "http://npm.meitu-int.com/@arco-design%2fweb-react/-/web-react-2.66.1.tgz", { "dependencies": { "@arco-design/color": "^0.4.0", "@babel/runtime": "^7.5.5", "b-tween": "^0.3.3", "b-validate": "^1.4.2", "compute-scroll-into-view": "^1.0.17", "dayjs": "^1.10.5", "lodash": "^4.17.21", "number-precision": "^1.3.1", "react-focus-lock": "^2.13.2", "react-is": "^18.2.0", "react-transition-group": "^4.3.0", "resize-observer-polyfill": "^1.5.1", "scroll-into-view-if-needed": "^2.2.20", "shallowequal": "^1.1.0" }, "peerDependencies": { "react": ">=16", "react-dom": ">=16" } }, "sha512-A3KKlNFHXldFrB2hGzLGu+RIGPYwizNnkYk3ttXEN/TnbzqhC1eGQqdo53j4hpLL0/DrQbSkOZNzlQgEdeQcGA=="],

    "@babel/helper-string-parser": ["@babel/helper-string-parser@7.27.1", "http://npm.meitu-int.com/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", {}, ""],

    "@babel/helper-validator-identifier": ["@babel/helper-validator-identifier@7.27.1", "http://npm.meitu-int.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", {}, ""],

    "@babel/parser": ["@babel/parser@7.27.2", "http://npm.meitu-int.com/@babel/parser/-/parser-7.27.2.tgz", { "dependencies": { "@babel/types": "^7.27.1" }, "bin": { "parser": "bin/babel-parser.js" } }, ""],

    "@babel/runtime": ["@babel/runtime@7.27.1", "http://npm.meitu-int.com/@babel/runtime/-/runtime-7.27.1.tgz", {}, ""],

    "@babel/types": ["@babel/types@7.27.1", "http://npm.meitu-int.com/@babel/types/-/types-7.27.1.tgz", { "dependencies": { "@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1" } }, ""],

    "@emnapi/core": ["@emnapi/core@1.4.5", "http://npm.meitu-int.com/@emnapi%2fcore/-/core-1.4.5.tgz", { "dependencies": { "@emnapi/wasi-threads": "1.0.4", "tslib": "^2.4.0" } }, "sha512-XsLw1dEOpkSX/WucdqUhPWP7hDxSvZiY+fsUC14h+FtQ2Ifni4znbBt8punRX+Uj2JG/uDb8nEHVKvrVlvdZ5Q=="],

    "@emnapi/runtime": ["@emnapi/runtime@1.4.5", "http://npm.meitu-int.com/@emnapi%2fruntime/-/runtime-1.4.5.tgz", { "dependencies": { "tslib": "^2.4.0" } }, "sha512-++LApOtY0pEEz1zrd9vy1/zXVaVJJ/EbAF3u0fXIzPJEDtnITsBGbbK0EkM72amhl/R5b+5xx0Y/QhcVOpuulg=="],

    "@emnapi/wasi-threads": ["@emnapi/wasi-threads@1.0.4", "http://npm.meitu-int.com/@emnapi%2fwasi-threads/-/wasi-threads-1.0.4.tgz", { "dependencies": { "tslib": "^2.4.0" } }, "sha512-PJR+bOmMOPH8AtcTGAyYNiuJ3/Fcoj2XN/gBEWzDIKh254XO+mM9XoXHk5GNEhodxeMznbg7BlRojVbKN+gC6g=="],

    "@eslint-community/eslint-utils": ["@eslint-community/eslint-utils@4.7.0", "http://npm.meitu-int.com/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz", { "dependencies": { "eslint-visitor-keys": "^3.4.3" }, "peerDependencies": { "eslint": "^6.0.0 || ^7.0.0 || >=8.0.0" } }, ""],

    "@eslint-community/regexpp": ["@eslint-community/regexpp@4.12.1", "http://npm.meitu-int.com/@eslint-community%2fregexpp/-/regexpp-4.12.1.tgz", {}, "sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ=="],

    "@eslint/config-array": ["@eslint/config-array@0.20.0", "http://npm.meitu-int.com/@eslint/config-array/-/config-array-0.20.0.tgz", { "dependencies": { "@eslint/object-schema": "^2.1.6", "debug": "^4.3.1", "minimatch": "^3.1.2" } }, ""],

    "@eslint/config-helpers": ["@eslint/config-helpers@0.2.2", "http://npm.meitu-int.com/@eslint/config-helpers/-/config-helpers-0.2.2.tgz", {}, ""],

    "@eslint/core": ["@eslint/core@0.13.0", "http://npm.meitu-int.com/@eslint/core/-/core-0.13.0.tgz", { "dependencies": { "@types/json-schema": "^7.0.15" } }, ""],

    "@eslint/eslintrc": ["@eslint/eslintrc@3.3.1", "http://npm.meitu-int.com/@eslint/eslintrc/-/eslintrc-3.3.1.tgz", { "dependencies": { "ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^10.0.1", "globals": "^14.0.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1" } }, ""],

    "@eslint/js": ["@eslint/js@9.26.0", "http://npm.meitu-int.com/@eslint/js/-/js-9.26.0.tgz", {}, ""],

    "@eslint/object-schema": ["@eslint/object-schema@2.1.6", "http://npm.meitu-int.com/@eslint/object-schema/-/object-schema-2.1.6.tgz", {}, ""],

    "@eslint/plugin-kit": ["@eslint/plugin-kit@0.2.8", "http://npm.meitu-int.com/@eslint/plugin-kit/-/plugin-kit-0.2.8.tgz", { "dependencies": { "@eslint/core": "^0.13.0", "levn": "^0.4.1" } }, ""],

    "@floating-ui/core": ["@floating-ui/core@1.7.0", "http://npm.meitu-int.com/@floating-ui/core/-/core-1.7.0.tgz", { "dependencies": { "@floating-ui/utils": "^0.2.9" } }, ""],

    "@floating-ui/dom": ["@floating-ui/dom@1.7.0", "http://npm.meitu-int.com/@floating-ui/dom/-/dom-1.7.0.tgz", { "dependencies": { "@floating-ui/core": "^1.7.0", "@floating-ui/utils": "^0.2.9" } }, ""],

    "@floating-ui/react-dom": ["@floating-ui/react-dom@2.1.2", "http://npm.meitu-int.com/@floating-ui%2freact-dom/-/react-dom-2.1.2.tgz", { "dependencies": { "@floating-ui/dom": "^1.0.0" }, "peerDependencies": { "react": ">=16.8.0", "react-dom": ">=16.8.0" } }, "sha512-06okr5cgPzMNBy+Ycse2A6udMi4bqwW/zgBF/rwjcNqWkyr82Mcg8b0vjX8OJpZFy/FKjJmw6wV7t44kK6kW7A=="],

    "@floating-ui/utils": ["@floating-ui/utils@0.2.9", "http://npm.meitu-int.com/@floating-ui%2futils/-/utils-0.2.9.tgz", {}, "sha512-MDWhGtE+eHw5JW7lq4qhc5yRLS11ERl1c7Z6Xd0a58DozHES6EnNNwUWbMiG4J9Cgj053Bhk8zvlhFYKVhULwg=="],

    "@humanfs/core": ["@humanfs/core@0.19.1", "http://npm.meitu-int.com/@humanfs%2fcore/-/core-0.19.1.tgz", {}, "sha512-5DyQ4+1JEUzejeK1JGICcideyfUbGixgS9jNgex5nqkW+cY7WZhxBigmieN5Qnw9ZosSNVC9KQKyb+GUaGyKUA=="],

    "@humanfs/node": ["@humanfs/node@0.16.6", "http://npm.meitu-int.com/@humanfs%2fnode/-/node-0.16.6.tgz", { "dependencies": { "@humanfs/core": "^0.19.1", "@humanwhocodes/retry": "^0.3.0" } }, "sha512-YuI2ZHQL78Q5HbhDiBA1X4LmYdXCKCMQIfw0pw7piHJwyREFebJUvrQN4cMssyES6x+vfUbx1CIpaQUKYdQZOw=="],

    "@humanwhocodes/module-importer": ["@humanwhocodes/module-importer@1.0.1", "http://npm.meitu-int.com/@humanwhocodes%2fmodule-importer/-/module-importer-1.0.1.tgz", {}, "sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA=="],

    "@humanwhocodes/retry": ["@humanwhocodes/retry@0.4.3", "http://npm.meitu-int.com/@humanwhocodes/retry/-/retry-0.4.3.tgz", {}, ""],

    "@isaacs/cliui": ["@isaacs/cliui@8.0.2", "http://npm.meitu-int.com/@isaacs%2fcliui/-/cliui-8.0.2.tgz", { "dependencies": { "string-width": "^5.1.2", "string-width-cjs": "npm:string-width@^4.2.0", "strip-ansi": "^7.0.1", "strip-ansi-cjs": "npm:strip-ansi@^6.0.1", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0" } }, "sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA=="],

    "@jridgewell/gen-mapping": ["@jridgewell/gen-mapping@0.3.8", "http://npm.meitu-int.com/@jridgewell%2fgen-mapping/-/gen-mapping-0.3.8.tgz", { "dependencies": { "@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24" } }, "sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA=="],

    "@jridgewell/resolve-uri": ["@jridgewell/resolve-uri@3.1.2", "http://npm.meitu-int.com/@jridgewell%2fresolve-uri/-/resolve-uri-3.1.2.tgz", {}, "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw=="],

    "@jridgewell/set-array": ["@jridgewell/set-array@1.2.1", "http://npm.meitu-int.com/@jridgewell%2fset-array/-/set-array-1.2.1.tgz", {}, "sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A=="],

    "@jridgewell/sourcemap-codec": ["@jridgewell/sourcemap-codec@1.5.0", "http://npm.meitu-int.com/@jridgewell%2fsourcemap-codec/-/sourcemap-codec-1.5.0.tgz", {}, "sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ=="],

    "@jridgewell/trace-mapping": ["@jridgewell/trace-mapping@0.3.25", "http://npm.meitu-int.com/@jridgewell%2ftrace-mapping/-/trace-mapping-0.3.25.tgz", { "dependencies": { "@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14" } }, "sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ=="],

    "@mapbox/node-pre-gyp": ["@mapbox/node-pre-gyp@1.0.11", "http://npm.meitu-int.com/@mapbox%2fnode-pre-gyp/-/node-pre-gyp-1.0.11.tgz", { "dependencies": { "detect-libc": "^2.0.0", "https-proxy-agent": "^5.0.0", "make-dir": "^3.1.0", "node-fetch": "^2.6.7", "nopt": "^5.0.0", "npmlog": "^5.0.1", "rimraf": "^3.0.2", "semver": "^7.3.5", "tar": "^6.1.11" }, "bin": { "node-pre-gyp": "bin/node-pre-gyp" } }, "sha512-Yhlar6v9WQgUp/He7BdgzOz8lqMQ8sU+jkCq7Wx8Myc5YFJLbEe7lgui/V7G1qB1DJykHSGwreceSaD60Y0PUQ=="],

    "@meitu/upload": ["@meitu/upload@4.8.5", "http://npm.meitu-int.com/@meitu/upload/-/upload-4.8.5.tgz", { "dependencies": { "@meitu/upload-core": "^4.8.5", "@meitu/upload-mini-program": "^4.8.5", "@meitu/upload-web": "^4.8.5", "tslib": "^2.4.0" } }, ""],

    "@meitu/upload-core": ["@meitu/upload-core@4.8.5", "http://npm.meitu-int.com/@meitu/upload-core/-/upload-core-4.8.5.tgz", { "dependencies": { "tslib": "^2.4.0" } }, ""],

    "@meitu/upload-mini-program": ["@meitu/upload-mini-program@4.8.5", "http://npm.meitu-int.com/@meitu/upload-mini-program/-/upload-mini-program-4.8.5.tgz", { "dependencies": { "@meitu/upload-core": "^4.8.5", "@meitu/upload-s3": "^4.8.5", "mime": "^2.0.0 || ^3.0.0", "tslib": "^2.4.0" } }, ""],

    "@meitu/upload-s3": ["@meitu/upload-s3@4.8.5", "http://npm.meitu-int.com/@meitu/upload-s3/-/upload-s3-4.8.5.tgz", { "dependencies": { "@meitu/upload-core": "^4.8.5", "crypto-js": "^4.0.0", "tslib": "^2.4.0" } }, ""],

    "@meitu/upload-web": ["@meitu/upload-web@4.8.5", "http://npm.meitu-int.com/@meitu/upload-web/-/upload-web-4.8.5.tgz", { "dependencies": { "@meitu/upload-core": "^4.8.5", "@meitu/upload-s3": "^4.8.5", "@types/qiniu-js": "2.5.2", "qiniu-js": "^2.5.5", "tslib": "^2.4.0" } }, ""],

    "@microsoft/api-extractor": ["@microsoft/api-extractor@7.52.8", "http://npm.meitu-int.com/@microsoft/api-extractor/-/api-extractor-7.52.8.tgz", { "dependencies": { "@microsoft/api-extractor-model": "7.30.6", "@microsoft/tsdoc": "~0.15.1", "@microsoft/tsdoc-config": "~0.17.1", "@rushstack/node-core-library": "5.13.1", "@rushstack/rig-package": "0.5.3", "@rushstack/terminal": "0.15.3", "@rushstack/ts-command-line": "5.0.1", "lodash": "~4.17.15", "minimatch": "~3.0.3", "resolve": "~1.22.1", "semver": "~7.5.4", "source-map": "~0.6.1", "typescript": "5.8.2" }, "bin": { "api-extractor": "bin/api-extractor" } }, ""],

    "@microsoft/api-extractor-model": ["@microsoft/api-extractor-model@7.30.6", "http://npm.meitu-int.com/@microsoft/api-extractor-model/-/api-extractor-model-7.30.6.tgz", { "dependencies": { "@microsoft/tsdoc": "~0.15.1", "@microsoft/tsdoc-config": "~0.17.1", "@rushstack/node-core-library": "5.13.1" } }, ""],

    "@microsoft/tsdoc": ["@microsoft/tsdoc@0.15.1", "http://npm.meitu-int.com/@microsoft%2ftsdoc/-/tsdoc-0.15.1.tgz", {}, "sha512-4aErSrCR/On/e5G2hDP0wjooqDdauzEbIq8hIkIe5pXV0rtWJZvdCEKL0ykZxex+IxIwBp0eGeV48hQN07dXtw=="],

    "@microsoft/tsdoc-config": ["@microsoft/tsdoc-config@0.17.1", "http://npm.meitu-int.com/@microsoft%2ftsdoc-config/-/tsdoc-config-0.17.1.tgz", { "dependencies": { "@microsoft/tsdoc": "0.15.1", "ajv": "~8.12.0", "jju": "~1.4.0", "resolve": "~1.22.2" } }, "sha512-UtjIFe0C6oYgTnad4q1QP4qXwLhe6tIpNTRStJ2RZEPIkqQPREAwE5spzVxsdn9UaEMUqhh0AqSx3X4nWAKXWw=="],

    "@modelcontextprotocol/sdk": ["@modelcontextprotocol/sdk@1.11.2", "http://npm.meitu-int.com/@modelcontextprotocol/sdk/-/sdk-1.11.2.tgz", { "dependencies": { "content-type": "^1.0.5", "cors": "^2.8.5", "cross-spawn": "^7.0.3", "eventsource": "^3.0.2", "express": "^5.0.1", "express-rate-limit": "^7.5.0", "pkce-challenge": "^5.0.0", "raw-body": "^3.0.0", "zod": "^3.23.8", "zod-to-json-schema": "^3.24.1" } }, ""],

    "@napi-rs/wasm-runtime": ["@napi-rs/wasm-runtime@1.0.1", "http://npm.meitu-int.com/@napi-rs%2fwasm-runtime/-/wasm-runtime-1.0.1.tgz", { "dependencies": { "@emnapi/core": "^1.4.5", "@emnapi/runtime": "^1.4.5", "@tybys/wasm-util": "^0.10.0" } }, "sha512-KVlQ/jgywZpixGCKMNwxStmmbYEMyokZpCf2YuIChhfJA2uqfAKNEM8INz7zzTo55iEXfBhIIs3VqYyqzDLj8g=="],

    "@nodelib/fs.scandir": ["@nodelib/fs.scandir@2.1.5", "http://npm.meitu-int.com/@nodelib%2ffs.scandir/-/fs.scandir-2.1.5.tgz", { "dependencies": { "@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9" } }, ""],

    "@nodelib/fs.stat": ["@nodelib/fs.stat@2.0.5", "http://npm.meitu-int.com/@nodelib%2ffs.stat/-/fs.stat-2.0.5.tgz", {}, ""],

    "@nodelib/fs.walk": ["@nodelib/fs.walk@1.2.8", "http://npm.meitu-int.com/@nodelib%2ffs.walk/-/fs.walk-1.2.8.tgz", { "dependencies": { "@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0" } }, ""],

    "@oxc-project/runtime": ["@oxc-project/runtime@0.77.3", "http://npm.meitu-int.com/@oxc-project%2fruntime/-/runtime-0.77.3.tgz", {}, "sha512-vsC/ewcGJ7xXnnwZkku7rpPH5Lxb5g4J+V6lD9eBTnRLmXVXM7Qu50y+ozD+UD5IXaSoVOvVMGTT4YSNCz2MQQ=="],

    "@oxc-project/types": ["@oxc-project/types@0.77.3", "http://npm.meitu-int.com/@oxc-project%2ftypes/-/types-0.77.3.tgz", {}, "sha512-5Vh+neJhhxuF0lYCjZXbxjqm2EO6YJ1jG+KuHntrd6VY67OMpYhWq2cZhUhy+xL9qLJVJRaeII7Xj9fciA6v7A=="],

    "@pkgjs/parseargs": ["@pkgjs/parseargs@0.11.0", "http://npm.meitu-int.com/@pkgjs%2fparseargs/-/parseargs-0.11.0.tgz", {}, "sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg=="],

    "@radix-ui/number": ["@radix-ui/number@1.1.1", "http://npm.meitu-int.com/@radix-ui/number/-/number-1.1.1.tgz", {}, ""],

    "@radix-ui/primitive": ["@radix-ui/primitive@1.1.2", "http://npm.meitu-int.com/@radix-ui%2fprimitive/-/primitive-1.1.2.tgz", {}, "sha512-XnbHrrprsNqZKQhStrSwgRUQzoCI1glLzdw79xiZPoofhGICeZRSQ3dIxAKH1gb3OHfNf4d6f+vAv3kil2eggA=="],

    "@radix-ui/react-arrow": ["@radix-ui/react-arrow@1.1.6", "http://npm.meitu-int.com/@radix-ui/react-arrow/-/react-arrow-1.1.6.tgz", { "dependencies": { "@radix-ui/react-primitive": "2.1.2" }, "peerDependencies": { "@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, ""],

    "@radix-ui/react-avatar": ["@radix-ui/react-avatar@1.1.9", "http://npm.meitu-int.com/@radix-ui/react-avatar/-/react-avatar-1.1.9.tgz", { "dependencies": { "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-is-hydrated": "0.1.0", "@radix-ui/react-use-layout-effect": "1.1.1" }, "peerDependencies": { "@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, ""],

    "@radix-ui/react-collection": ["@radix-ui/react-collection@1.1.6", "http://npm.meitu-int.com/@radix-ui/react-collection/-/react-collection-1.1.6.tgz", { "dependencies": { "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-slot": "1.2.2" }, "peerDependencies": { "@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, ""],

    "@radix-ui/react-compose-refs": ["@radix-ui/react-compose-refs@1.1.2", "http://npm.meitu-int.com/@radix-ui%2freact-compose-refs/-/react-compose-refs-1.1.2.tgz", { "peerDependencies": { "@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, "sha512-z4eqJvfiNnFMHIIvXP3CY57y2WJs5g2v3X0zm9mEJkrkNv4rDxu+sg9Jh8EkXyeqBkB7SOcboo9dMVqhyrACIg=="],

    "@radix-ui/react-context": ["@radix-ui/react-context@1.1.2", "http://npm.meitu-int.com/@radix-ui%2freact-context/-/react-context-1.1.2.tgz", { "peerDependencies": { "@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, "sha512-jCi/QKUM2r1Ju5a3J64TH2A5SpKAgh0LpknyqdQ4m6DCV0xJ2HG1xARRwNGPQfi1SLdLWZ1OJz6F4OMBBNiGJA=="],

    "@radix-ui/react-direction": ["@radix-ui/react-direction@1.1.1", "http://npm.meitu-int.com/@radix-ui/react-direction/-/react-direction-1.1.1.tgz", { "peerDependencies": { "@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, ""],

    "@radix-ui/react-dismissable-layer": ["@radix-ui/react-dismissable-layer@1.1.9", "http://npm.meitu-int.com/@radix-ui/react-dismissable-layer/-/react-dismissable-layer-1.1.9.tgz", { "dependencies": { "@radix-ui/primitive": "1.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-escape-keydown": "1.1.1" }, "peerDependencies": { "@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, ""],

    "@radix-ui/react-dropdown-menu": ["@radix-ui/react-dropdown-menu@2.1.14", "http://npm.meitu-int.com/@radix-ui/react-dropdown-menu/-/react-dropdown-menu-2.1.14.tgz", { "dependencies": { "@radix-ui/primitive": "1.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-id": "1.1.1", "@radix-ui/react-menu": "2.1.14", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-use-controllable-state": "1.2.2" }, "peerDependencies": { "@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, ""],

    "@radix-ui/react-focus-guards": ["@radix-ui/react-focus-guards@1.1.2", "http://npm.meitu-int.com/@radix-ui/react-focus-guards/-/react-focus-guards-1.1.2.tgz", { "peerDependencies": { "@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, ""],

    "@radix-ui/react-focus-scope": ["@radix-ui/react-focus-scope@1.1.6", "http://npm.meitu-int.com/@radix-ui/react-focus-scope/-/react-focus-scope-1.1.6.tgz", { "dependencies": { "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-use-callback-ref": "1.1.1" }, "peerDependencies": { "@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, ""],

    "@radix-ui/react-id": ["@radix-ui/react-id@1.1.1", "http://npm.meitu-int.com/@radix-ui%2freact-id/-/react-id-1.1.1.tgz", { "dependencies": { "@radix-ui/react-use-layout-effect": "1.1.1" }, "peerDependencies": { "@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, "sha512-kGkGegYIdQsOb4XjsfM97rXsiHaBwco+hFI66oO4s9LU+PLAC5oJ7khdOVFxkhsmlbpUqDAvXw11CluXP+jkHg=="],

    "@radix-ui/react-menu": ["@radix-ui/react-menu@2.1.14", "http://npm.meitu-int.com/@radix-ui/react-menu/-/react-menu-2.1.14.tgz", { "dependencies": { "@radix-ui/primitive": "1.1.2", "@radix-ui/react-collection": "1.1.6", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.9", "@radix-ui/react-focus-guards": "1.1.2", "@radix-ui/react-focus-scope": "1.1.6", "@radix-ui/react-id": "1.1.1", "@radix-ui/react-popper": "1.2.6", "@radix-ui/react-portal": "1.1.8", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-roving-focus": "1.1.9", "@radix-ui/react-slot": "1.2.2", "@radix-ui/react-use-callback-ref": "1.1.1", "aria-hidden": "^1.2.4", "react-remove-scroll": "^2.6.3" }, "peerDependencies": { "@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, ""],

    "@radix-ui/react-popover": ["@radix-ui/react-popover@1.1.13", "http://npm.meitu-int.com/@radix-ui/react-popover/-/react-popover-1.1.13.tgz", { "dependencies": { "@radix-ui/primitive": "1.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-dismissable-layer": "1.1.9", "@radix-ui/react-focus-guards": "1.1.2", "@radix-ui/react-focus-scope": "1.1.6", "@radix-ui/react-id": "1.1.1", "@radix-ui/react-popper": "1.2.6", "@radix-ui/react-portal": "1.1.8", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-slot": "1.2.2", "@radix-ui/react-use-controllable-state": "1.2.2", "aria-hidden": "^1.2.4", "react-remove-scroll": "^2.6.3" }, "peerDependencies": { "@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, ""],

    "@radix-ui/react-popper": ["@radix-ui/react-popper@1.2.6", "http://npm.meitu-int.com/@radix-ui/react-popper/-/react-popper-1.2.6.tgz", { "dependencies": { "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-arrow": "1.1.6", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/rect": "1.1.1" }, "peerDependencies": { "@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, ""],

    "@radix-ui/react-portal": ["@radix-ui/react-portal@1.1.8", "http://npm.meitu-int.com/@radix-ui/react-portal/-/react-portal-1.1.8.tgz", { "dependencies": { "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-use-layout-effect": "1.1.1" }, "peerDependencies": { "@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, ""],

    "@radix-ui/react-presence": ["@radix-ui/react-presence@1.1.4", "http://npm.meitu-int.com/@radix-ui/react-presence/-/react-presence-1.1.4.tgz", { "dependencies": { "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1" }, "peerDependencies": { "@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, ""],

    "@radix-ui/react-primitive": ["@radix-ui/react-primitive@2.1.2", "http://npm.meitu-int.com/@radix-ui/react-primitive/-/react-primitive-2.1.2.tgz", { "dependencies": { "@radix-ui/react-slot": "1.2.2" }, "peerDependencies": { "@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, ""],

    "@radix-ui/react-roving-focus": ["@radix-ui/react-roving-focus@1.1.9", "http://npm.meitu-int.com/@radix-ui/react-roving-focus/-/react-roving-focus-1.1.9.tgz", { "dependencies": { "@radix-ui/primitive": "1.1.2", "@radix-ui/react-collection": "1.1.6", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-id": "1.1.1", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2" }, "peerDependencies": { "@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, ""],

    "@radix-ui/react-scroll-area": ["@radix-ui/react-scroll-area@1.2.8", "http://npm.meitu-int.com/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.8.tgz", { "dependencies": { "@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1" }, "peerDependencies": { "@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, ""],

    "@radix-ui/react-slider": ["@radix-ui/react-slider@1.3.4", "http://npm.meitu-int.com/@radix-ui/react-slider/-/react-slider-1.3.4.tgz", { "dependencies": { "@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-collection": "1.1.6", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-use-controllable-state": "1.2.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-size": "1.1.1" }, "peerDependencies": { "@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, ""],

    "@radix-ui/react-slot": ["@radix-ui/react-slot@1.2.2", "http://npm.meitu-int.com/@radix-ui/react-slot/-/react-slot-1.2.2.tgz", { "dependencies": { "@radix-ui/react-compose-refs": "1.1.2" }, "peerDependencies": { "@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, ""],

    "@radix-ui/react-toast": ["@radix-ui/react-toast@1.2.13", "http://npm.meitu-int.com/@radix-ui/react-toast/-/react-toast-1.2.13.tgz", { "dependencies": { "@radix-ui/primitive": "1.1.2", "@radix-ui/react-collection": "1.1.6", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-dismissable-layer": "1.1.9", "@radix-ui/react-portal": "1.1.8", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.2" }, "peerDependencies": { "@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, ""],

    "@radix-ui/react-tooltip": ["@radix-ui/react-tooltip@1.2.6", "http://npm.meitu-int.com/@radix-ui/react-tooltip/-/react-tooltip-1.2.6.tgz", { "dependencies": { "@radix-ui/primitive": "1.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-dismissable-layer": "1.1.9", "@radix-ui/react-id": "1.1.1", "@radix-ui/react-popper": "1.2.6", "@radix-ui/react-portal": "1.1.8", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-slot": "1.2.2", "@radix-ui/react-use-controllable-state": "1.2.2", "@radix-ui/react-visually-hidden": "1.2.2" }, "peerDependencies": { "@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, ""],

    "@radix-ui/react-use-callback-ref": ["@radix-ui/react-use-callback-ref@1.1.1", "http://npm.meitu-int.com/@radix-ui%2freact-use-callback-ref/-/react-use-callback-ref-1.1.1.tgz", { "peerDependencies": { "@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, "sha512-FkBMwD+qbGQeMu1cOHnuGB6x4yzPjho8ap5WtbEJ26umhgqVXbhekKUQO+hZEL1vU92a3wHwdp0HAcqAUF5iDg=="],

    "@radix-ui/react-use-controllable-state": ["@radix-ui/react-use-controllable-state@1.2.2", "http://npm.meitu-int.com/@radix-ui/react-use-controllable-state/-/react-use-controllable-state-1.2.2.tgz", { "dependencies": { "@radix-ui/react-use-effect-event": "0.0.2", "@radix-ui/react-use-layout-effect": "1.1.1" }, "peerDependencies": { "@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, ""],

    "@radix-ui/react-use-effect-event": ["@radix-ui/react-use-effect-event@0.0.2", "http://npm.meitu-int.com/@radix-ui/react-use-effect-event/-/react-use-effect-event-0.0.2.tgz", { "dependencies": { "@radix-ui/react-use-layout-effect": "1.1.1" }, "peerDependencies": { "@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, ""],

    "@radix-ui/react-use-escape-keydown": ["@radix-ui/react-use-escape-keydown@1.1.1", "http://npm.meitu-int.com/@radix-ui%2freact-use-escape-keydown/-/react-use-escape-keydown-1.1.1.tgz", { "dependencies": { "@radix-ui/react-use-callback-ref": "1.1.1" }, "peerDependencies": { "@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, "sha512-Il0+boE7w/XebUHyBjroE+DbByORGR9KKmITzbR7MyQ4akpORYP/ZmbhAr0DG7RmmBqoOnZdy2QlvajJ2QA59g=="],

    "@radix-ui/react-use-is-hydrated": ["@radix-ui/react-use-is-hydrated@0.1.0", "http://npm.meitu-int.com/@radix-ui/react-use-is-hydrated/-/react-use-is-hydrated-0.1.0.tgz", { "dependencies": { "use-sync-external-store": "^1.5.0" }, "peerDependencies": { "@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, ""],

    "@radix-ui/react-use-layout-effect": ["@radix-ui/react-use-layout-effect@1.1.1", "http://npm.meitu-int.com/@radix-ui%2freact-use-layout-effect/-/react-use-layout-effect-1.1.1.tgz", { "peerDependencies": { "@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, "sha512-RbJRS4UWQFkzHTTwVymMTUv8EqYhOp8dOOviLj2ugtTiXRaRQS7GLGxZTLL1jWhMeoSCf5zmcZkqTl9IiYfXcQ=="],

    "@radix-ui/react-use-previous": ["@radix-ui/react-use-previous@1.1.1", "http://npm.meitu-int.com/@radix-ui/react-use-previous/-/react-use-previous-1.1.1.tgz", { "peerDependencies": { "@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, ""],

    "@radix-ui/react-use-rect": ["@radix-ui/react-use-rect@1.1.1", "http://npm.meitu-int.com/@radix-ui%2freact-use-rect/-/react-use-rect-1.1.1.tgz", { "dependencies": { "@radix-ui/rect": "1.1.1" }, "peerDependencies": { "@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, "sha512-QTYuDesS0VtuHNNvMh+CjlKJ4LJickCMUAqjlE3+j8w+RlRpwyX3apEQKGFzbZGdo7XNG1tXa+bQqIE7HIXT2w=="],

    "@radix-ui/react-use-size": ["@radix-ui/react-use-size@1.1.1", "http://npm.meitu-int.com/@radix-ui%2freact-use-size/-/react-use-size-1.1.1.tgz", { "dependencies": { "@radix-ui/react-use-layout-effect": "1.1.1" }, "peerDependencies": { "@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, "sha512-ewrXRDTAqAXlkl6t/fkXWNAhFX9I+CkKlw6zjEwk86RSPKwZr3xpBRso655aqYafwtnbpHLj6toFzmd6xdVptQ=="],

    "@radix-ui/react-visually-hidden": ["@radix-ui/react-visually-hidden@1.2.2", "http://npm.meitu-int.com/@radix-ui/react-visually-hidden/-/react-visually-hidden-1.2.2.tgz", { "dependencies": { "@radix-ui/react-primitive": "2.1.2" }, "peerDependencies": { "@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc" } }, ""],

    "@radix-ui/rect": ["@radix-ui/rect@1.1.1", "http://npm.meitu-int.com/@radix-ui%2frect/-/rect-1.1.1.tgz", {}, "sha512-HPwpGIzkl28mWyZqG52jiqDJ12waP11Pa1lGoiyUkIEuMLBP0oeK/C89esbXrxsky5we7dfd8U58nm0SgAWpVw=="],

    "@rolldown/binding-android-arm64": ["@rolldown/binding-android-arm64@1.0.0-beta.29", "http://npm.meitu-int.com/@rolldown%2fbinding-android-arm64/-/binding-android-arm64-1.0.0-beta.29.tgz", { "os": "android", "cpu": "arm64" }, "sha512-pDv7gg59Gdy80eFmMkEqXEaoJi3Y9W/a9T3z9M4t8Ma8aVXNldvSy9UgtlX7AK7DPqF8tULnmIZ2Z3rvGMz/NQ=="],

    "@rolldown/binding-darwin-arm64": ["@rolldown/binding-darwin-arm64@1.0.0-beta.29", "http://npm.meitu-int.com/@rolldown%2fbinding-darwin-arm64/-/binding-darwin-arm64-1.0.0-beta.29.tgz", { "os": "darwin", "cpu": "arm64" }, "sha512-fPqR6TfTqbzgKKCQYtcCS+Dms91YcptTbdlwJ13DxOUgMe8LgDIVsLLlEykfm7ijJd5mM4zNw0Hr2CJb6kvQZw=="],

    "@rolldown/binding-darwin-x64": ["@rolldown/binding-darwin-x64@1.0.0-beta.29", "http://npm.meitu-int.com/@rolldown%2fbinding-darwin-x64/-/binding-darwin-x64-1.0.0-beta.29.tgz", { "os": "darwin", "cpu": "x64" }, "sha512-7Z4qosL0xN8i6++txHOEPCVP3/lcGLOvftUJOWATZ5aDkDskwcZDa66BGiJt/K1/DgW4kpRVmnGWUWAORHBbFA=="],

    "@rolldown/binding-freebsd-x64": ["@rolldown/binding-freebsd-x64@1.0.0-beta.29", "http://npm.meitu-int.com/@rolldown%2fbinding-freebsd-x64/-/binding-freebsd-x64-1.0.0-beta.29.tgz", { "os": "freebsd", "cpu": "x64" }, "sha512-0HLTfPW5Glh608s76qgayN/nPsXPchNUumavf7W5nh1eMG6qBsOO7Q1QaK0v4un7qtsn3IA/1Tgq0ZgNc0dbeg=="],

    "@rolldown/binding-linux-arm-gnueabihf": ["@rolldown/binding-linux-arm-gnueabihf@1.0.0-beta.29", "http://npm.meitu-int.com/@rolldown%2fbinding-linux-arm-gnueabihf/-/binding-linux-arm-gnueabihf-1.0.0-beta.29.tgz", { "os": "linux", "cpu": "arm" }, "sha512-QNboxdVTJOZS4zP8kA2+XUwAegejd5QNSH5zVR4neqG2AfbxRcMFzSVRkJHN6yDaaKweD/4sUvXfmef6p/7zsw=="],

    "@rolldown/binding-linux-arm64-gnu": ["@rolldown/binding-linux-arm64-gnu@1.0.0-beta.29", "http://npm.meitu-int.com/@rolldown%2fbinding-linux-arm64-gnu/-/binding-linux-arm64-gnu-1.0.0-beta.29.tgz", { "os": "linux", "cpu": "arm64" }, "sha512-hzBmOtYdC4369XxN2SNJ3oBlXKWNif3ieWBT+oh/qvAeox4fQR0ngqyh+kIGOufBnP5Zc2rqJf9LzIbJw3Tx/Q=="],

    "@rolldown/binding-linux-arm64-musl": ["@rolldown/binding-linux-arm64-musl@1.0.0-beta.29", "http://npm.meitu-int.com/@rolldown%2fbinding-linux-arm64-musl/-/binding-linux-arm64-musl-1.0.0-beta.29.tgz", { "os": "linux", "cpu": "arm64" }, "sha512-6B35GmFJJ4RX88OgubrnUmuJBUgRh6/OTXIpy8m/VUnoc683lufIPo26HW/0LxLgxp2GM7KHr3LOULcVxbqq4Q=="],

    "@rolldown/binding-linux-arm64-ohos": ["@rolldown/binding-linux-arm64-ohos@1.0.0-beta.29", "http://npm.meitu-int.com/@rolldown%2fbinding-linux-arm64-ohos/-/binding-linux-arm64-ohos-1.0.0-beta.29.tgz", { "os": "none", "cpu": "arm64" }, "sha512-z3ru8fUCunQM8q9I7RbDVMT5cxzxVVVBNNKM5/qAQQrdObd1u8g0LR5z0yLtaFWzybwLVdPtJDRcXtLm5tOBFA=="],

    "@rolldown/binding-linux-x64-gnu": ["@rolldown/binding-linux-x64-gnu@1.0.0-beta.29", "http://npm.meitu-int.com/@rolldown%2fbinding-linux-x64-gnu/-/binding-linux-x64-gnu-1.0.0-beta.29.tgz", { "os": "linux", "cpu": "x64" }, "sha512-n6fs4L7j99MIiI6vKhQDdyScv4/uMAPtIMkB0zGbUX8MKWT1osym1hvWVdlENjnS/Phf0zzhjyOgoFDzdhI1cQ=="],

    "@rolldown/binding-linux-x64-musl": ["@rolldown/binding-linux-x64-musl@1.0.0-beta.29", "http://npm.meitu-int.com/@rolldown%2fbinding-linux-x64-musl/-/binding-linux-x64-musl-1.0.0-beta.29.tgz", { "os": "linux", "cpu": "x64" }, "sha512-C5hcJgtDN4rp6/WsPTQSDVUWrdnIC//ynMGcUIh1O0anm9KnSy47zKQ5D9EqtlEKvO+2PPqmyUVJ2DTq18nlVA=="],

    "@rolldown/binding-wasm32-wasi": ["@rolldown/binding-wasm32-wasi@1.0.0-beta.29", "http://npm.meitu-int.com/@rolldown%2fbinding-wasm32-wasi/-/binding-wasm32-wasi-1.0.0-beta.29.tgz", { "dependencies": { "@napi-rs/wasm-runtime": "^1.0.1" }, "cpu": "none" }, "sha512-lMN1IBItdZFO182Sdus9oVuNDqyIymn/bsR5KwgeGaiqLsrmpQHBSLwkS/nKJO1nzYlpGDRugFSpnrSJ5ZmihQ=="],

    "@rolldown/binding-win32-arm64-msvc": ["@rolldown/binding-win32-arm64-msvc@1.0.0-beta.29", "http://npm.meitu-int.com/@rolldown%2fbinding-win32-arm64-msvc/-/binding-win32-arm64-msvc-1.0.0-beta.29.tgz", { "os": "win32", "cpu": "arm64" }, "sha512-0UrXCUAOrbWdyVJskzjtne/4d3YMMhhhpBnob3SeF4jAvbKYqPhCZJ71pP7yUpvbowGXXTnHWpKfitg4Sovmtw=="],

    "@rolldown/binding-win32-ia32-msvc": ["@rolldown/binding-win32-ia32-msvc@1.0.0-beta.29", "http://npm.meitu-int.com/@rolldown%2fbinding-win32-ia32-msvc/-/binding-win32-ia32-msvc-1.0.0-beta.29.tgz", { "os": "win32", "cpu": "ia32" }, "sha512-YX0OYL1dcB7rPnsndpEa68fytYyZZj1iaWzH7momFB2oBS2lXAe1UrrDWcdLoUXdzPIyzpvtBCiS2XcDgYG7ag=="],

    "@rolldown/binding-win32-x64-msvc": ["@rolldown/binding-win32-x64-msvc@1.0.0-beta.29", "http://npm.meitu-int.com/@rolldown%2fbinding-win32-x64-msvc/-/binding-win32-x64-msvc-1.0.0-beta.29.tgz", { "os": "win32", "cpu": "x64" }, "sha512-azrPWbV+NZiCFNs59AgH9Y6vFKHoAI6T/XtKKsoLxkPyP1LpbdgL5eqRfeWz+GCAUY9qhDOC4hH1GjFG8PrZIg=="],

    "@rolldown/pluginutils": ["@rolldown/pluginutils@1.0.0-beta.29", "http://npm.meitu-int.com/@rolldown%2fpluginutils/-/pluginutils-1.0.0-beta.29.tgz", {}, "sha512-NIJgOsMjbxAXvoGq/X0gD7VPMQ8j9g0BiDaNjVNVjvl+iKXxL3Jre0v31RmBYeLEmkbj2s02v8vFTbUXi5XS2Q=="],

    "@rollup/pluginutils": ["@rollup/pluginutils@5.1.4", "http://npm.meitu-int.com/@rollup%2fpluginutils/-/pluginutils-5.1.4.tgz", { "dependencies": { "@types/estree": "^1.0.0", "estree-walker": "^2.0.2", "picomatch": "^4.0.2" }, "peerDependencies": { "rollup": "^1.20.0||^2.0.0||^3.0.0||^4.0.0" } }, "sha512-USm05zrsFxYLPdWWq+K3STlWiT/3ELn3RcV5hJMghpeAIhxfsUIg6mt12CBJBInWMV4VneoV7SfGv8xIwo2qNQ=="],

    "@rollup/rollup-darwin-arm64": ["@rollup/rollup-darwin-arm64@4.40.2", "http://npm.meitu-int.com/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.40.2.tgz", { "os": "darwin", "cpu": "arm64" }, ""],

    "@rushstack/node-core-library": ["@rushstack/node-core-library@5.13.1", "http://npm.meitu-int.com/@rushstack/node-core-library/-/node-core-library-5.13.1.tgz", { "dependencies": { "ajv": "~8.13.0", "ajv-draft-04": "~1.0.0", "ajv-formats": "~3.0.1", "fs-extra": "~11.3.0", "import-lazy": "~4.0.0", "jju": "~1.4.0", "resolve": "~1.22.1", "semver": "~7.5.4" }, "peerDependencies": { "@types/node": "*" } }, ""],

    "@rushstack/rig-package": ["@rushstack/rig-package@0.5.3", "http://npm.meitu-int.com/@rushstack%2frig-package/-/rig-package-0.5.3.tgz", { "dependencies": { "resolve": "~1.22.1", "strip-json-comments": "~3.1.1" } }, "sha512-olzSSjYrvCNxUFZowevC3uz8gvKr3WTpHQ7BkpjtRpA3wK+T0ybep/SRUMfr195gBzJm5gaXw0ZMgjIyHqJUow=="],

    "@rushstack/terminal": ["@rushstack/terminal@0.15.3", "http://npm.meitu-int.com/@rushstack/terminal/-/terminal-0.15.3.tgz", { "dependencies": { "@rushstack/node-core-library": "5.13.1", "supports-color": "~8.1.1" }, "peerDependencies": { "@types/node": "*" } }, ""],

    "@rushstack/ts-command-line": ["@rushstack/ts-command-line@5.0.1", "http://npm.meitu-int.com/@rushstack/ts-command-line/-/ts-command-line-5.0.1.tgz", { "dependencies": { "@rushstack/terminal": "0.15.3", "@types/argparse": "1.0.38", "argparse": "~1.0.9", "string-argv": "~0.3.1" } }, ""],

    "@swc/core": ["@swc/core@1.11.24", "http://npm.meitu-int.com/@swc/core/-/core-1.11.24.tgz", { "dependencies": { "@swc/counter": "^0.1.3", "@swc/types": "^0.1.21" }, "optionalDependencies": { "@swc/core-darwin-arm64": "1.11.24" }, "peerDependencies": { "@swc/helpers": ">=0.5.17" }, "optionalPeers": ["@swc/helpers"] }, ""],

    "@swc/core-darwin-arm64": ["@swc/core-darwin-arm64@1.11.24", "http://npm.meitu-int.com/@swc/core-darwin-arm64/-/core-darwin-arm64-1.11.24.tgz", { "os": "darwin", "cpu": "arm64" }, ""],

    "@swc/counter": ["@swc/counter@0.1.3", "http://npm.meitu-int.com/@swc%2fcounter/-/counter-0.1.3.tgz", {}, "sha512-e2BR4lsJkkRlKZ/qCHPw9ZaSxc0MVUd7gtbtaB7aMvHeJVYe8sOB8DBZkP2DtISHGSku9sCK6T6cnY0CtXrOCQ=="],

    "@swc/types": ["@swc/types@0.1.21", "http://npm.meitu-int.com/@swc/types/-/types-0.1.21.tgz", { "dependencies": { "@swc/counter": "^0.1.3" } }, ""],

    "@tanstack/query-core": ["@tanstack/query-core@5.81.5", "http://npm.meitu-int.com/@tanstack%2fquery-core/-/query-core-5.81.5.tgz", {}, "sha512-ZJOgCy/z2qpZXWaj/oxvodDx07XcQa9BF92c0oINjHkoqUPsmm3uG08HpTaviviZ/N9eP1f9CM7mKSEkIo7O1Q=="],

    "@tanstack/react-query": ["@tanstack/react-query@5.81.5", "http://npm.meitu-int.com/@tanstack%2freact-query/-/react-query-5.81.5.tgz", { "dependencies": { "@tanstack/query-core": "5.81.5" }, "peerDependencies": { "react": "^18 || ^19" } }, "sha512-lOf2KqRRiYWpQT86eeeftAGnjuTR35myTP8MXyvHa81VlomoAWNEd8x5vkcAfQefu0qtYCvyqLropFZqgI2EQw=="],

    "@tootallnate/once": ["@tootallnate/once@2.0.0", "http://npm.meitu-int.com/@tootallnate%2fonce/-/once-2.0.0.tgz", {}, ""],

    "@tybys/wasm-util": ["@tybys/wasm-util@0.10.0", "http://npm.meitu-int.com/@tybys%2fwasm-util/-/wasm-util-0.10.0.tgz", { "dependencies": { "tslib": "^2.4.0" } }, "sha512-VyyPYFlOMNylG45GoAe0xDoLwWuowvf92F9kySqzYh8vmYm7D2u4iUJKa1tOUpS70Ku13ASrOkS4ScXFsTaCNQ=="],

    "@types/argparse": ["@types/argparse@1.0.38", "http://npm.meitu-int.com/@types%2fargparse/-/argparse-1.0.38.tgz", {}, ""],

    "@types/estree": ["@types/estree@1.0.7", "http://npm.meitu-int.com/@types/estree/-/estree-1.0.7.tgz", {}, ""],

    "@types/events": ["@types/events@3.0.3", "http://npm.meitu-int.com/@types%2fevents/-/events-3.0.3.tgz", {}, "sha512-trOc4AAUThEz9hapPtSd7wf5tiQKvTtu5b371UxXdTuqzIh0ArcRspRP0i0Viu+LXstIQ1z96t1nsPxT9ol01g=="],

    "@types/json-schema": ["@types/json-schema@7.0.15", "http://npm.meitu-int.com/@types%2fjson-schema/-/json-schema-7.0.15.tgz", {}, "sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA=="],

    "@types/lodash": ["@types/lodash@4.17.16", "http://npm.meitu-int.com/@types/lodash/-/lodash-4.17.16.tgz", {}, ""],

    "@types/lodash-es": ["@types/lodash-es@4.17.12", "http://npm.meitu-int.com/@types%2flodash-es/-/lodash-es-4.17.12.tgz", { "dependencies": { "@types/lodash": "*" } }, "sha512-0NgftHUcV4v34VhXm8QBSftKVXtbkBG3ViCjs6+eJ5a6y6Mi/jiFGPc1sC7QK+9BFhWrURE3EOggmWaSxL9OzQ=="],

    "@types/node": ["@types/node@22.15.17", "http://npm.meitu-int.com/@types/node/-/node-22.15.17.tgz", { "dependencies": { "undici-types": "~6.21.0" } }, ""],

    "@types/prop-types": ["@types/prop-types@15.7.14", "http://npm.meitu-int.com/@types%2fprop-types/-/prop-types-15.7.14.tgz", {}, "sha512-gNMvNH49DJ7OJYv+KAKn0Xp45p8PLl6zo2YnvDIbTd4J6MER2BmWN49TG7n9LvkyihINxeKW8+3bfS2yDC9dzQ=="],

    "@types/qiniu-js": ["@types/qiniu-js@2.5.2", "http://npm.meitu-int.com/@types/qiniu-js/-/qiniu-js-2.5.2.tgz", {}, ""],

    "@types/react": ["@types/react@18.3.21", "http://npm.meitu-int.com/@types/react/-/react-18.3.21.tgz", { "dependencies": { "@types/prop-types": "*", "csstype": "^3.0.2" } }, ""],

    "@types/react-dom": ["@types/react-dom@18.3.7", "http://npm.meitu-int.com/@types/react-dom/-/react-dom-18.3.7.tgz", { "peerDependencies": { "@types/react": "^18.0.0" } }, ""],

    "@types/react-transition-group": ["@types/react-transition-group@4.4.12", "http://npm.meitu-int.com/@types/react-transition-group/-/react-transition-group-4.4.12.tgz", { "peerDependencies": { "@types/react": "*" } }, ""],

    "@typescript-eslint/eslint-plugin": ["@typescript-eslint/eslint-plugin@8.32.1", "http://npm.meitu-int.com/@typescript-eslint/eslint-plugin/-/eslint-plugin-8.32.1.tgz", { "dependencies": { "@eslint-community/regexpp": "^4.10.0", "@typescript-eslint/scope-manager": "8.32.1", "@typescript-eslint/type-utils": "8.32.1", "@typescript-eslint/utils": "8.32.1", "@typescript-eslint/visitor-keys": "8.32.1", "graphemer": "^1.4.0", "ignore": "^7.0.0", "natural-compare": "^1.4.0", "ts-api-utils": "^2.1.0" }, "peerDependencies": { "@typescript-eslint/parser": "^8.0.0 || ^8.0.0-alpha.0", "eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0" } }, ""],

    "@typescript-eslint/parser": ["@typescript-eslint/parser@8.32.1", "http://npm.meitu-int.com/@typescript-eslint/parser/-/parser-8.32.1.tgz", { "dependencies": { "@typescript-eslint/scope-manager": "8.32.1", "@typescript-eslint/types": "8.32.1", "@typescript-eslint/typescript-estree": "8.32.1", "@typescript-eslint/visitor-keys": "8.32.1", "debug": "^4.3.4" }, "peerDependencies": { "eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0" } }, ""],

    "@typescript-eslint/scope-manager": ["@typescript-eslint/scope-manager@8.32.1", "http://npm.meitu-int.com/@typescript-eslint/scope-manager/-/scope-manager-8.32.1.tgz", { "dependencies": { "@typescript-eslint/types": "8.32.1", "@typescript-eslint/visitor-keys": "8.32.1" } }, ""],

    "@typescript-eslint/type-utils": ["@typescript-eslint/type-utils@8.32.1", "http://npm.meitu-int.com/@typescript-eslint/type-utils/-/type-utils-8.32.1.tgz", { "dependencies": { "@typescript-eslint/typescript-estree": "8.32.1", "@typescript-eslint/utils": "8.32.1", "debug": "^4.3.4", "ts-api-utils": "^2.1.0" }, "peerDependencies": { "eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0" } }, ""],

    "@typescript-eslint/types": ["@typescript-eslint/types@8.32.1", "http://npm.meitu-int.com/@typescript-eslint/types/-/types-8.32.1.tgz", {}, ""],

    "@typescript-eslint/typescript-estree": ["@typescript-eslint/typescript-estree@8.32.1", "http://npm.meitu-int.com/@typescript-eslint/typescript-estree/-/typescript-estree-8.32.1.tgz", { "dependencies": { "@typescript-eslint/types": "8.32.1", "@typescript-eslint/visitor-keys": "8.32.1", "debug": "^4.3.4", "fast-glob": "^3.3.2", "is-glob": "^4.0.3", "minimatch": "^9.0.4", "semver": "^7.6.0", "ts-api-utils": "^2.1.0" }, "peerDependencies": { "typescript": ">=4.8.4 <5.9.0" } }, ""],

    "@typescript-eslint/utils": ["@typescript-eslint/utils@8.32.1", "http://npm.meitu-int.com/@typescript-eslint/utils/-/utils-8.32.1.tgz", { "dependencies": { "@eslint-community/eslint-utils": "^4.7.0", "@typescript-eslint/scope-manager": "8.32.1", "@typescript-eslint/types": "8.32.1", "@typescript-eslint/typescript-estree": "8.32.1" }, "peerDependencies": { "eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0" } }, ""],

    "@typescript-eslint/visitor-keys": ["@typescript-eslint/visitor-keys@8.32.1", "http://npm.meitu-int.com/@typescript-eslint/visitor-keys/-/visitor-keys-8.32.1.tgz", { "dependencies": { "@typescript-eslint/types": "8.32.1", "eslint-visitor-keys": "^4.2.0" } }, ""],

    "@velipso/polybool": ["@velipso/polybool@2.0.11", "http://npm.meitu-int.com/@velipso%2fpolybool/-/polybool-2.0.11.tgz", { "dependencies": { "tslib": "^2.6.2" } }, "sha512-NT/1UOeNumugXhLBChGNnnFqYQ/CqyC/OpkwpSj+pGjEYak1OMm4mA4vsPpzm23NRwuUIbtr9kJRwEDL7VnrNA=="],

    "@vitejs/plugin-react-swc": ["@vitejs/plugin-react-swc@3.9.0", "http://npm.meitu-int.com/@vitejs/plugin-react-swc/-/plugin-react-swc-3.9.0.tgz", { "dependencies": { "@swc/core": "^1.11.21" }, "peerDependencies": { "vite": "^4 || ^5 || ^6" } }, ""],

    "@volar/language-core": ["@volar/language-core@2.4.13", "http://npm.meitu-int.com/@volar/language-core/-/language-core-2.4.13.tgz", { "dependencies": { "@volar/source-map": "2.4.13" } }, ""],

    "@volar/source-map": ["@volar/source-map@2.4.13", "http://npm.meitu-int.com/@volar/source-map/-/source-map-2.4.13.tgz", {}, ""],

    "@volar/typescript": ["@volar/typescript@2.4.13", "http://npm.meitu-int.com/@volar/typescript/-/typescript-2.4.13.tgz", { "dependencies": { "@volar/language-core": "2.4.13", "path-browserify": "^1.0.1", "vscode-uri": "^3.0.8" } }, ""],

    "@vue/compiler-core": ["@vue/compiler-core@3.5.13", "http://npm.meitu-int.com/@vue%2fcompiler-core/-/compiler-core-3.5.13.tgz", { "dependencies": { "@babel/parser": "^7.25.3", "@vue/shared": "3.5.13", "entities": "^4.5.0", "estree-walker": "^2.0.2", "source-map-js": "^1.2.0" } }, "sha512-oOdAkwqUfW1WqpwSYJce06wvt6HljgY3fGeM9NcVA1HaYOij3mZG9Rkysn0OHuyUAGMbEbARIpsG+LPVlBJ5/Q=="],

    "@vue/compiler-dom": ["@vue/compiler-dom@3.5.13", "http://npm.meitu-int.com/@vue%2fcompiler-dom/-/compiler-dom-3.5.13.tgz", { "dependencies": { "@vue/compiler-core": "3.5.13", "@vue/shared": "3.5.13" } }, "sha512-ZOJ46sMOKUjO3e94wPdCzQ6P1Lx/vhp2RSvfaab88Ajexs0AHeV0uasYhi99WPaogmBlRHNRuly8xV75cNTMDA=="],

    "@vue/compiler-vue2": ["@vue/compiler-vue2@2.7.16", "http://npm.meitu-int.com/@vue%2fcompiler-vue2/-/compiler-vue2-2.7.16.tgz", { "dependencies": { "de-indent": "^1.0.2", "he": "^1.2.0" } }, "sha512-qYC3Psj9S/mfu9uVi5WvNZIzq+xnXMhOwbTFKKDD7b1lhpnn71jXSFdTQ+WsIEk0ONCd7VV2IMm7ONl6tbQ86A=="],

    "@vue/language-core": ["@vue/language-core@2.2.0", "http://npm.meitu-int.com/@vue%2flanguage-core/-/language-core-2.2.0.tgz", { "dependencies": { "@volar/language-core": "~2.4.11", "@vue/compiler-dom": "^3.5.0", "@vue/compiler-vue2": "^2.7.16", "@vue/shared": "^3.5.0", "alien-signals": "^0.4.9", "minimatch": "^9.0.3", "muggle-string": "^0.4.1", "path-browserify": "^1.0.1" }, "peerDependencies": { "typescript": "*" } }, "sha512-O1ZZFaaBGkKbsRfnVH1ifOK1/1BUkyK+3SQsfnh6PmMmD4qJcTU8godCeA96jjDRTL6zgnK7YzCHfaUlH2r0Mw=="],

    "@vue/shared": ["@vue/shared@3.5.13", "http://npm.meitu-int.com/@vue%2fshared/-/shared-3.5.13.tgz", {}, "sha512-/hnE/qP5ZoGpol0a5mDi45bOd7t3tjYJBjsgCsivow7D48cJeV5l05RD82lPqi7gRiphZM37rnhW1l6ZoCNNnQ=="],

    "@webgpu/types": ["@webgpu/types@0.1.21", "http://npm.meitu-int.com/@webgpu%2ftypes/-/types-0.1.21.tgz", {}, "sha512-pUrWq3V5PiSGFLeLxoGqReTZmiiXwY3jRkIG5sLLKjyqNxrwm/04b4nw7LSmGWJcKk59XOM/YRTUwOzo4MMlow=="],

    "abab": ["abab@2.0.6", "http://npm.meitu-int.com/abab/-/abab-2.0.6.tgz", {}, "sha512-j2afSsaIENvHZN2B8GOpF566vZ5WVk5opAiMTvWgaQT8DkbOqsTfvNAvHoRGU2zzP8cPoqys+xHTRDWW8L+/BA=="],

    "abbrev": ["abbrev@1.1.1", "http://npm.meitu-int.com/abbrev/-/abbrev-1.1.1.tgz", {}, ""],

    "accepts": ["accepts@2.0.0", "http://npm.meitu-int.com/accepts/-/accepts-2.0.0.tgz", { "dependencies": { "mime-types": "^3.0.0", "negotiator": "^1.0.0" } }, ""],

    "acorn": ["acorn@8.14.1", "http://npm.meitu-int.com/acorn/-/acorn-8.14.1.tgz", { "bin": "bin/acorn" }, ""],

    "acorn-globals": ["acorn-globals@7.0.1", "http://npm.meitu-int.com/acorn-globals/-/acorn-globals-7.0.1.tgz", { "dependencies": { "acorn": "^8.1.0", "acorn-walk": "^8.0.2" } }, "sha512-umOSDSDrfHbTNPuNpC2NSnnA3LUrqpevPb4T9jRx4MagXNS0rs+gwiTcAvqCRmsD6utzsrzNt+ebm00SNWiC3Q=="],

    "acorn-jsx": ["acorn-jsx@5.3.2", "http://npm.meitu-int.com/acorn-jsx/-/acorn-jsx-5.3.2.tgz", { "peerDependencies": { "acorn": "^6.0.0 || ^7.0.0 || ^8.0.0" } }, ""],

    "acorn-walk": ["acorn-walk@8.3.4", "http://npm.meitu-int.com/acorn-walk/-/acorn-walk-8.3.4.tgz", { "dependencies": { "acorn": "^8.11.0" } }, "sha512-ueEepnujpqee2o5aIYnvHU6C0A42MNdsIDeqy5BydrkuC5R1ZuUFnm27EeFJGoEHJQgn3uleRvmTXaJgfXbt4g=="],

    "agent-base": ["agent-base@6.0.2", "http://npm.meitu-int.com/agent-base/-/agent-base-6.0.2.tgz", { "dependencies": { "debug": "4" } }, ""],

    "ajv": ["ajv@6.12.6", "http://npm.meitu-int.com/ajv/-/ajv-6.12.6.tgz", { "dependencies": { "fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2" } }, ""],

    "ajv-draft-04": ["ajv-draft-04@1.0.0", "http://npm.meitu-int.com/ajv-draft-04/-/ajv-draft-04-1.0.0.tgz", { "peerDependencies": { "ajv": "^8.5.0" } }, "sha512-mv00Te6nmYbRp5DCwclxtt7yV/joXJPGS7nM+97GdxvuttCOfgI3K4U25zboyeX0O+myI8ERluxQe5wljMmVIw=="],

    "ajv-formats": ["ajv-formats@3.0.1", "http://npm.meitu-int.com/ajv-formats/-/ajv-formats-3.0.1.tgz", { "dependencies": { "ajv": "^8.0.0" }, "peerDependencies": { "ajv": "^8.0.0" } }, "sha512-8iUql50EUR+uUcdRQ3HDqa6EVyo3docL8g5WJ3FNcWmu62IbkGUue/pEyLBW8VGKKucTPgqeks4fIU1DA4yowQ=="],

    "alien-signals": ["alien-signals@0.4.14", "http://npm.meitu-int.com/alien-signals/-/alien-signals-0.4.14.tgz", {}, "sha512-itUAVzhczTmP2U5yX67xVpsbbOiquusbWVyA9N+sy6+r6YVbFkahXvNCeEPWEOMhwDYwbVbGHFkVL03N9I5g+Q=="],

    "ansi-regex": ["ansi-regex@5.0.1", "http://npm.meitu-int.com/ansi-regex/-/ansi-regex-5.0.1.tgz", {}, ""],

    "ansi-styles": ["ansi-styles@4.3.0", "http://npm.meitu-int.com/ansi-styles/-/ansi-styles-4.3.0.tgz", { "dependencies": { "color-convert": "^2.0.1" } }, ""],

    "ansis": ["ansis@4.1.0", "http://npm.meitu-int.com/ansis/-/ansis-4.1.0.tgz", {}, "sha512-BGcItUBWSMRgOCe+SVZJ+S7yTRG0eGt9cXAHev72yuGcY23hnLA7Bky5L/xLyPINoSN95geovfBkqoTlNZYa7w=="],

    "any-promise": ["any-promise@1.3.0", "http://npm.meitu-int.com/any-promise/-/any-promise-1.3.0.tgz", {}, ""],

    "anymatch": ["anymatch@3.1.3", "http://npm.meitu-int.com/anymatch/-/anymatch-3.1.3.tgz", { "dependencies": { "normalize-path": "^3.0.0", "picomatch": "^2.0.4" } }, "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw=="],

    "aproba": ["aproba@2.0.0", "http://npm.meitu-int.com/aproba/-/aproba-2.0.0.tgz", {}, ""],

    "are-we-there-yet": ["are-we-there-yet@2.0.0", "http://npm.meitu-int.com/are-we-there-yet/-/are-we-there-yet-2.0.0.tgz", { "dependencies": { "delegates": "^1.0.0", "readable-stream": "^3.6.0" } }, ""],

    "arg": ["arg@5.0.2", "http://npm.meitu-int.com/arg/-/arg-5.0.2.tgz", {}, "sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg=="],

    "argparse": ["argparse@2.0.1", "http://npm.meitu-int.com/argparse/-/argparse-2.0.1.tgz", {}, ""],

    "aria-hidden": ["aria-hidden@1.2.4", "http://npm.meitu-int.com/aria-hidden/-/aria-hidden-1.2.4.tgz", { "dependencies": { "tslib": "^2.0.0" } }, "sha512-y+CcFFwelSXpLZk/7fMB2mUbGtX9lKycf1MWJ7CaTIERyitVlyQx6C+sxcROU2BAJ24OiZyK+8wj2i8AlBoS3A=="],

    "asynckit": ["asynckit@0.4.0", "http://npm.meitu-int.com/asynckit/-/asynckit-0.4.0.tgz", {}, ""],

    "autoprefixer": ["autoprefixer@10.4.21", "http://npm.meitu-int.com/autoprefixer/-/autoprefixer-10.4.21.tgz", { "dependencies": { "browserslist": "^4.24.4", "caniuse-lite": "^1.0.30001702", "fraction.js": "^4.3.7", "normalize-range": "^0.1.2", "picocolors": "^1.1.1", "postcss-value-parser": "^4.2.0" }, "peerDependencies": { "postcss": "^8.1.0" }, "bin": "bin/autoprefixer" }, ""],

    "await-to-js": ["await-to-js@3.0.0", "http://npm.meitu-int.com/await-to-js/-/await-to-js-3.0.0.tgz", {}, ""],

    "axios": ["axios@1.9.0", "http://npm.meitu-int.com/axios/-/axios-1.9.0.tgz", { "dependencies": { "follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0" } }, ""],

    "b-tween": ["b-tween@0.3.3", "http://npm.meitu-int.com/b-tween/-/b-tween-0.3.3.tgz", {}, ""],

    "b-validate": ["b-validate@1.5.3", "http://npm.meitu-int.com/b-validate/-/b-validate-1.5.3.tgz", {}, ""],

    "balanced-match": ["balanced-match@1.0.2", "http://npm.meitu-int.com/balanced-match/-/balanced-match-1.0.2.tgz", {}, ""],

    "binary-extensions": ["binary-extensions@2.3.0", "http://npm.meitu-int.com/binary-extensions/-/binary-extensions-2.3.0.tgz", {}, "sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw=="],

    "body-parser": ["body-parser@2.2.0", "http://npm.meitu-int.com/body-parser/-/body-parser-2.2.0.tgz", { "dependencies": { "bytes": "^3.1.2", "content-type": "^1.0.5", "debug": "^4.4.0", "http-errors": "^2.0.0", "iconv-lite": "^0.6.3", "on-finished": "^2.4.1", "qs": "^6.14.0", "raw-body": "^3.0.0", "type-is": "^2.0.0" } }, ""],

    "brace-expansion": ["brace-expansion@1.1.11", "http://npm.meitu-int.com/brace-expansion/-/brace-expansion-1.1.11.tgz", { "dependencies": { "balanced-match": "^1.0.0", "concat-map": "0.0.1" } }, ""],

    "braces": ["braces@3.0.3", "http://npm.meitu-int.com/braces/-/braces-3.0.3.tgz", { "dependencies": { "fill-range": "^7.1.1" } }, "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA=="],

    "browserslist": ["browserslist@4.24.5", "http://npm.meitu-int.com/browserslist/-/browserslist-4.24.5.tgz", { "dependencies": { "caniuse-lite": "^1.0.30001716", "electron-to-chromium": "^1.5.149", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3" }, "bin": "cli.js" }, ""],

    "bytes": ["bytes@3.1.2", "http://npm.meitu-int.com/bytes/-/bytes-3.1.2.tgz", {}, ""],

    "call-bind-apply-helpers": ["call-bind-apply-helpers@1.0.2", "http://npm.meitu-int.com/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", { "dependencies": { "es-errors": "^1.3.0", "function-bind": "^1.1.2" } }, ""],

    "call-bound": ["call-bound@1.0.4", "http://npm.meitu-int.com/call-bound/-/call-bound-1.0.4.tgz", { "dependencies": { "call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0" } }, ""],

    "callsites": ["callsites@3.1.0", "http://npm.meitu-int.com/callsites/-/callsites-3.1.0.tgz", {}, ""],

    "camelcase-css": ["camelcase-css@2.0.1", "http://npm.meitu-int.com/camelcase-css/-/camelcase-css-2.0.1.tgz", {}, ""],

    "caniuse-lite": ["caniuse-lite@1.0.30001717", "http://npm.meitu-int.com/caniuse-lite/-/caniuse-lite-1.0.30001717.tgz", {}, ""],

    "canvas": ["canvas@2.11.2", "http://npm.meitu-int.com/canvas/-/canvas-2.11.2.tgz", { "dependencies": { "@mapbox/node-pre-gyp": "^1.0.0", "nan": "^2.17.0", "simple-get": "^3.0.3" } }, "sha512-ItanGBMrmRV7Py2Z+Xhs7cT+FNt5K0vPL4p9EZ/UX/Mu7hFbkxSjKF2KVtPwX7UYWp7dRKnrTvReflgrItJbdw=="],

    "canvaskit-wasm": ["canvaskit-wasm@0.39.1", "http://npm.meitu-int.com/canvaskit-wasm/-/canvaskit-wasm-0.39.1.tgz", { "dependencies": { "@webgpu/types": "0.1.21" } }, "sha512-Gy3lCmhUdKq+8bvDrs9t8+qf7RvcjuQn+we7vTVVyqgOVO1UVfHpsnBxkTZw+R4ApEJ3D5fKySl9TU11hmjl/A=="],

    "chalk": ["chalk@4.1.2", "http://npm.meitu-int.com/chalk/-/chalk-4.1.2.tgz", { "dependencies": { "ansi-styles": "^4.1.0", "supports-color": "^7.1.0" } }, ""],

    "chokidar": ["chokidar@3.6.0", "http://npm.meitu-int.com/chokidar/-/chokidar-3.6.0.tgz", { "dependencies": { "anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0" }, "optionalDependencies": { "fsevents": "~2.3.2" } }, "sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw=="],

    "chownr": ["chownr@2.0.0", "http://npm.meitu-int.com/chownr/-/chownr-2.0.0.tgz", {}, ""],

    "class-variance-authority": ["class-variance-authority@0.7.1", "http://npm.meitu-int.com/class-variance-authority/-/class-variance-authority-0.7.1.tgz", { "dependencies": { "clsx": "^2.1.1" } }, "sha512-Ka+9Trutv7G8M6WT6SeiRWz792K5qEqIGEGzXKhAE6xOWAY6pPH8U+9IY3oCMv6kqTmLsv7Xh/2w2RigkePMsg=="],

    "clsx": ["clsx@2.1.1", "http://npm.meitu-int.com/clsx/-/clsx-2.1.1.tgz", {}, "sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA=="],

    "color": ["color@3.2.1", "http://npm.meitu-int.com/color/-/color-3.2.1.tgz", { "dependencies": { "color-convert": "^1.9.3", "color-string": "^1.6.0" } }, ""],

    "color-convert": ["color-convert@2.0.1", "http://npm.meitu-int.com/color-convert/-/color-convert-2.0.1.tgz", { "dependencies": { "color-name": "~1.1.4" } }, ""],

    "color-name": ["color-name@1.1.4", "http://npm.meitu-int.com/color-name/-/color-name-1.1.4.tgz", {}, ""],

    "color-string": ["color-string@1.9.1", "http://npm.meitu-int.com/color-string/-/color-string-1.9.1.tgz", { "dependencies": { "color-name": "^1.0.0", "simple-swizzle": "^0.2.2" } }, ""],

    "color-support": ["color-support@1.1.3", "http://npm.meitu-int.com/color-support/-/color-support-1.1.3.tgz", { "bin": "bin.js" }, ""],

    "combined-stream": ["combined-stream@1.0.8", "http://npm.meitu-int.com/combined-stream/-/combined-stream-1.0.8.tgz", { "dependencies": { "delayed-stream": "~1.0.0" } }, ""],

    "commander": ["commander@4.1.1", "http://npm.meitu-int.com/commander/-/commander-4.1.1.tgz", {}, ""],

    "compare-versions": ["compare-versions@6.1.1", "http://npm.meitu-int.com/compare-versions/-/compare-versions-6.1.1.tgz", {}, "sha512-4hm4VPpIecmlg59CHXnRDnqGplJFrbLG4aFEl5vl6cK1u76ws3LLvX7ikFnTDl5vo39sjWD6AaDPYodJp/NNHg=="],

    "compute-scroll-into-view": ["compute-scroll-into-view@1.0.20", "http://npm.meitu-int.com/compute-scroll-into-view/-/compute-scroll-into-view-1.0.20.tgz", {}, ""],

    "concat-map": ["concat-map@0.0.1", "http://npm.meitu-int.com/concat-map/-/concat-map-0.0.1.tgz", {}, ""],

    "confbox": ["confbox@0.2.2", "http://npm.meitu-int.com/confbox/-/confbox-0.2.2.tgz", {}, ""],

    "console-control-strings": ["console-control-strings@1.1.0", "http://npm.meitu-int.com/console-control-strings/-/console-control-strings-1.1.0.tgz", {}, ""],

    "content-disposition": ["content-disposition@1.0.0", "http://npm.meitu-int.com/content-disposition/-/content-disposition-1.0.0.tgz", { "dependencies": { "safe-buffer": "5.2.1" } }, ""],

    "content-type": ["content-type@1.0.5", "http://npm.meitu-int.com/content-type/-/content-type-1.0.5.tgz", {}, ""],

    "cookie": ["cookie@0.7.2", "http://npm.meitu-int.com/cookie/-/cookie-0.7.2.tgz", {}, ""],

    "cookie-signature": ["cookie-signature@1.2.2", "http://npm.meitu-int.com/cookie-signature/-/cookie-signature-1.2.2.tgz", {}, ""],

    "cors": ["cors@2.8.5", "http://npm.meitu-int.com/cors/-/cors-2.8.5.tgz", { "dependencies": { "object-assign": "^4", "vary": "^1" } }, ""],

    "cross-spawn": ["cross-spawn@7.0.6", "http://npm.meitu-int.com/cross-spawn/-/cross-spawn-7.0.6.tgz", { "dependencies": { "path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1" } }, "sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA=="],

    "crypto-js": ["crypto-js@4.2.0", "http://npm.meitu-int.com/crypto-js/-/crypto-js-4.2.0.tgz", {}, ""],

    "cssesc": ["cssesc@3.0.0", "http://npm.meitu-int.com/cssesc/-/cssesc-3.0.0.tgz", { "bin": "bin/cssesc" }, ""],

    "cssom": ["cssom@0.5.0", "http://npm.meitu-int.com/cssom/-/cssom-0.5.0.tgz", {}, ""],

    "cssstyle": ["cssstyle@2.3.0", "http://npm.meitu-int.com/cssstyle/-/cssstyle-2.3.0.tgz", { "dependencies": { "cssom": "~0.3.6" } }, ""],

    "csstype": ["csstype@3.1.3", "http://npm.meitu-int.com/csstype/-/csstype-3.1.3.tgz", {}, "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw=="],

    "data-urls": ["data-urls@3.0.2", "http://npm.meitu-int.com/data-urls/-/data-urls-3.0.2.tgz", { "dependencies": { "abab": "^2.0.6", "whatwg-mimetype": "^3.0.0", "whatwg-url": "^11.0.0" } }, "sha512-Jy/tj3ldjZJo63sVAvg6LHt2mHvl4V6AgRAmNDtLdm7faqtsx+aJG42rsyCo9JCoRVKwPFzKlIPx3DIibwSIaQ=="],

    "dayjs": ["dayjs@1.11.13", "http://npm.meitu-int.com/dayjs/-/dayjs-1.11.13.tgz", {}, ""],

    "de-indent": ["de-indent@1.0.2", "http://npm.meitu-int.com/de-indent/-/de-indent-1.0.2.tgz", {}, ""],

    "debug": ["debug@4.4.0", "http://npm.meitu-int.com/debug/-/debug-4.4.0.tgz", { "dependencies": { "ms": "^2.1.3" } }, "sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA=="],

    "decimal.js": ["decimal.js@10.5.0", "http://npm.meitu-int.com/decimal.js/-/decimal.js-10.5.0.tgz", {}, ""],

    "decompress-response": ["decompress-response@4.2.1", "http://npm.meitu-int.com/decompress-response/-/decompress-response-4.2.1.tgz", { "dependencies": { "mimic-response": "^2.0.0" } }, ""],

    "deep-is": ["deep-is@0.1.4", "http://npm.meitu-int.com/deep-is/-/deep-is-0.1.4.tgz", {}, ""],

    "delayed-stream": ["delayed-stream@1.0.0", "http://npm.meitu-int.com/delayed-stream/-/delayed-stream-1.0.0.tgz", {}, ""],

    "delegates": ["delegates@1.0.0", "http://npm.meitu-int.com/delegates/-/delegates-1.0.0.tgz", {}, ""],

    "depd": ["depd@2.0.0", "http://npm.meitu-int.com/depd/-/depd-2.0.0.tgz", {}, ""],

    "dequal": ["dequal@2.0.3", "http://npm.meitu-int.com/dequal/-/dequal-2.0.3.tgz", {}, ""],

    "detect-libc": ["detect-libc@2.0.4", "http://npm.meitu-int.com/detect-libc/-/detect-libc-2.0.4.tgz", {}, ""],

    "detect-node-es": ["detect-node-es@1.1.0", "http://npm.meitu-int.com/detect-node-es/-/detect-node-es-1.1.0.tgz", {}, ""],

    "didyoumean": ["didyoumean@1.2.2", "http://npm.meitu-int.com/didyoumean/-/didyoumean-1.2.2.tgz", {}, ""],

    "dlv": ["dlv@1.1.3", "http://npm.meitu-int.com/dlv/-/dlv-1.1.3.tgz", {}, ""],

    "dom-helpers": ["dom-helpers@5.2.1", "http://npm.meitu-int.com/dom-helpers/-/dom-helpers-5.2.1.tgz", { "dependencies": { "@babel/runtime": "^7.8.7", "csstype": "^3.0.2" } }, ""],

    "domexception": ["domexception@4.0.0", "http://npm.meitu-int.com/domexception/-/domexception-4.0.0.tgz", { "dependencies": { "webidl-conversions": "^7.0.0" } }, ""],

    "dunder-proto": ["dunder-proto@1.0.1", "http://npm.meitu-int.com/dunder-proto/-/dunder-proto-1.0.1.tgz", { "dependencies": { "call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0" } }, ""],

    "eastasianwidth": ["eastasianwidth@0.2.0", "http://npm.meitu-int.com/eastasianwidth/-/eastasianwidth-0.2.0.tgz", {}, ""],

    "ee-first": ["ee-first@1.1.1", "http://npm.meitu-int.com/ee-first/-/ee-first-1.1.1.tgz", {}, ""],

    "electron-to-chromium": ["electron-to-chromium@1.5.152", "http://npm.meitu-int.com/electron-to-chromium/-/electron-to-chromium-1.5.152.tgz", {}, ""],

    "emoji-regex": ["emoji-regex@8.0.0", "http://npm.meitu-int.com/emoji-regex/-/emoji-regex-8.0.0.tgz", {}, ""],

    "encodeurl": ["encodeurl@2.0.0", "http://npm.meitu-int.com/encodeurl/-/encodeurl-2.0.0.tgz", {}, ""],

    "entities": ["entities@6.0.0", "http://npm.meitu-int.com/entities/-/entities-6.0.0.tgz", {}, ""],

    "es-define-property": ["es-define-property@1.0.1", "http://npm.meitu-int.com/es-define-property/-/es-define-property-1.0.1.tgz", {}, ""],

    "es-errors": ["es-errors@1.3.0", "http://npm.meitu-int.com/es-errors/-/es-errors-1.3.0.tgz", {}, ""],

    "es-object-atoms": ["es-object-atoms@1.1.1", "http://npm.meitu-int.com/es-object-atoms/-/es-object-atoms-1.1.1.tgz", { "dependencies": { "es-errors": "^1.3.0" } }, ""],

    "es-set-tostringtag": ["es-set-tostringtag@2.1.0", "http://npm.meitu-int.com/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz", { "dependencies": { "es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2" } }, ""],

    "escalade": ["escalade@3.2.0", "http://npm.meitu-int.com/escalade/-/escalade-3.2.0.tgz", {}, "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA=="],

    "escape-html": ["escape-html@1.0.3", "http://npm.meitu-int.com/escape-html/-/escape-html-1.0.3.tgz", {}, ""],

    "escape-string-regexp": ["escape-string-regexp@4.0.0", "http://npm.meitu-int.com/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", {}, ""],

    "escodegen": ["escodegen@2.1.0", "http://npm.meitu-int.com/escodegen/-/escodegen-2.1.0.tgz", { "dependencies": { "esprima": "^4.0.1", "estraverse": "^5.2.0", "esutils": "^2.0.2" }, "optionalDependencies": { "source-map": "~0.6.1" }, "bin": { "escodegen": "bin/escodegen.js", "esgenerate": "bin/esgenerate.js" } }, "sha512-2NlIDTwUWJN0mRPQOdtQBzbUHvdGY2P1VXSyU83Q3xKxM7WHX2Ql8dKq782Q9TgQUNOLEzEYu9bzLNj1q88I5w=="],

    "eslint": ["eslint@9.26.0", "http://npm.meitu-int.com/eslint/-/eslint-9.26.0.tgz", { "dependencies": { "@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.12.1", "@eslint/config-array": "^0.20.0", "@eslint/config-helpers": "^0.2.1", "@eslint/core": "^0.13.0", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "9.26.0", "@eslint/plugin-kit": "^0.2.8", "@humanfs/node": "^0.16.6", "@humanwhocodes/module-importer": "^1.0.1", "@humanwhocodes/retry": "^0.4.2", "@modelcontextprotocol/sdk": "^1.8.0", "@types/estree": "^1.0.6", "@types/json-schema": "^7.0.15", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.6", "debug": "^4.3.2", "escape-string-regexp": "^4.0.0", "eslint-scope": "^8.3.0", "eslint-visitor-keys": "^4.2.0", "espree": "^10.3.0", "esquery": "^1.5.0", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^8.0.0", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "json-stable-stringify-without-jsonify": "^1.0.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3", "zod": "^3.24.2" }, "peerDependencies": { "jiti": "*" }, "bin": "bin/eslint.js" }, ""],

    "eslint-plugin-react-hooks": ["eslint-plugin-react-hooks@5.2.0", "http://npm.meitu-int.com/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-5.2.0.tgz", { "peerDependencies": { "eslint": "^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0" } }, ""],

    "eslint-plugin-react-refresh": ["eslint-plugin-react-refresh@0.4.20", "http://npm.meitu-int.com/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.20.tgz", { "peerDependencies": { "eslint": ">=8.40" } }, ""],

    "eslint-scope": ["eslint-scope@8.3.0", "http://npm.meitu-int.com/eslint-scope/-/eslint-scope-8.3.0.tgz", { "dependencies": { "esrecurse": "^4.3.0", "estraverse": "^5.2.0" } }, ""],

    "eslint-visitor-keys": ["eslint-visitor-keys@4.2.0", "http://npm.meitu-int.com/eslint-visitor-keys/-/eslint-visitor-keys-4.2.0.tgz", {}, "sha512-UyLnSehNt62FFhSwjZlHmeokpRK59rcz29j+F1/aDgbkbRTk7wIc9XzdoasMUbRNKDM0qQt/+BJ4BrpFeABemw=="],

    "espree": ["espree@10.3.0", "http://npm.meitu-int.com/espree/-/espree-10.3.0.tgz", { "dependencies": { "acorn": "^8.14.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^4.2.0" } }, "sha512-0QYC8b24HWY8zjRnDTL6RiHfDbAWn63qb4LMj1Z4b076A4une81+z03Kg7l7mn/48PUTqoLptSXez8oknU8Clg=="],

    "esprima": ["esprima@4.0.1", "http://npm.meitu-int.com/esprima/-/esprima-4.0.1.tgz", { "bin": { "esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js" } }, ""],

    "esquery": ["esquery@1.6.0", "http://npm.meitu-int.com/esquery/-/esquery-1.6.0.tgz", { "dependencies": { "estraverse": "^5.1.0" } }, "sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg=="],

    "esrecurse": ["esrecurse@4.3.0", "http://npm.meitu-int.com/esrecurse/-/esrecurse-4.3.0.tgz", { "dependencies": { "estraverse": "^5.2.0" } }, ""],

    "estraverse": ["estraverse@5.3.0", "http://npm.meitu-int.com/estraverse/-/estraverse-5.3.0.tgz", {}, ""],

    "estree-walker": ["estree-walker@2.0.2", "http://npm.meitu-int.com/estree-walker/-/estree-walker-2.0.2.tgz", {}, ""],

    "esutils": ["esutils@2.0.3", "http://npm.meitu-int.com/esutils/-/esutils-2.0.3.tgz", {}, ""],

    "etag": ["etag@1.8.1", "http://npm.meitu-int.com/etag/-/etag-1.8.1.tgz", {}, ""],

    "events": ["events@3.3.0", "http://npm.meitu-int.com/events/-/events-3.3.0.tgz", {}, ""],

    "eventsource": ["eventsource@3.0.7", "http://npm.meitu-int.com/eventsource/-/eventsource-3.0.7.tgz", { "dependencies": { "eventsource-parser": "^3.0.1" } }, ""],

    "eventsource-parser": ["eventsource-parser@3.0.1", "http://npm.meitu-int.com/eventsource-parser/-/eventsource-parser-3.0.1.tgz", {}, ""],

    "express": ["express@5.1.0", "http://npm.meitu-int.com/express/-/express-5.1.0.tgz", { "dependencies": { "accepts": "^2.0.0", "body-parser": "^2.2.0", "content-disposition": "^1.0.0", "content-type": "^1.0.5", "cookie": "^0.7.1", "cookie-signature": "^1.2.1", "debug": "^4.4.0", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "etag": "^1.8.1", "finalhandler": "^2.1.0", "fresh": "^2.0.0", "http-errors": "^2.0.0", "merge-descriptors": "^2.0.0", "mime-types": "^3.0.0", "on-finished": "^2.4.1", "once": "^1.4.0", "parseurl": "^1.3.3", "proxy-addr": "^2.0.7", "qs": "^6.14.0", "range-parser": "^1.2.1", "router": "^2.2.0", "send": "^1.1.0", "serve-static": "^2.2.0", "statuses": "^2.0.1", "type-is": "^2.0.1", "vary": "^1.1.2" } }, ""],

    "express-rate-limit": ["express-rate-limit@7.5.0", "http://npm.meitu-int.com/express-rate-limit/-/express-rate-limit-7.5.0.tgz", { "peerDependencies": { "express": "^4.11 || 5 || ^5.0.0-beta.1" } }, ""],

    "exsolve": ["exsolve@1.0.5", "http://npm.meitu-int.com/exsolve/-/exsolve-1.0.5.tgz", {}, ""],

    "fabric": ["fabric@6.6.5", "http://npm.meitu-int.com/fabric/-/fabric-6.6.5.tgz", { "optionalDependencies": { "canvas": "^2.11.2", "jsdom": "^20.0.1" } }, ""],

    "fast-deep-equal": ["fast-deep-equal@3.1.3", "http://npm.meitu-int.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", {}, ""],

    "fast-glob": ["fast-glob@3.3.3", "http://npm.meitu-int.com/fast-glob/-/fast-glob-3.3.3.tgz", { "dependencies": { "@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.8" } }, "sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg=="],

    "fast-json-stable-stringify": ["fast-json-stable-stringify@2.1.0", "http://npm.meitu-int.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", {}, ""],

    "fast-levenshtein": ["fast-levenshtein@2.0.6", "http://npm.meitu-int.com/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", {}, ""],

    "fastq": ["fastq@1.19.1", "http://npm.meitu-int.com/fastq/-/fastq-1.19.1.tgz", { "dependencies": { "reusify": "^1.0.4" } }, ""],

    "fdir": ["fdir@6.4.6", "http://npm.meitu-int.com/fdir/-/fdir-6.4.6.tgz", { "peerDependencies": { "picomatch": "^3 || ^4" }, "optionalPeers": ["picomatch"] }, "sha512-hiFoqpyZcfNm1yc4u8oWCf9A2c4D3QjCrks3zmoVKVxpQRzmPNar1hUJcBG2RQHvEVGDN+Jm81ZheVLAQMK6+w=="],

    "file-entry-cache": ["file-entry-cache@8.0.0", "http://npm.meitu-int.com/file-entry-cache/-/file-entry-cache-8.0.0.tgz", { "dependencies": { "flat-cache": "^4.0.0" } }, "sha512-XXTUwCvisa5oacNGRP9SfNtYBNAMi+RPwBFmblZEF7N7swHYQS6/Zfk7SRwx4D5j3CH211YNRco1DEMNVfZCnQ=="],

    "fill-range": ["fill-range@7.1.1", "http://npm.meitu-int.com/fill-range/-/fill-range-7.1.1.tgz", { "dependencies": { "to-regex-range": "^5.0.1" } }, "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg=="],

    "finalhandler": ["finalhandler@2.1.0", "http://npm.meitu-int.com/finalhandler/-/finalhandler-2.1.0.tgz", { "dependencies": { "debug": "^4.4.0", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "on-finished": "^2.4.1", "parseurl": "^1.3.3", "statuses": "^2.0.1" } }, ""],

    "find-up": ["find-up@5.0.0", "http://npm.meitu-int.com/find-up/-/find-up-5.0.0.tgz", { "dependencies": { "locate-path": "^6.0.0", "path-exists": "^4.0.0" } }, ""],

    "flat-cache": ["flat-cache@4.0.1", "http://npm.meitu-int.com/flat-cache/-/flat-cache-4.0.1.tgz", { "dependencies": { "flatted": "^3.2.9", "keyv": "^4.5.4" } }, "sha512-f7ccFPK3SXFHpx15UIGyRJ/FJQctuKZ0zVuN3frBo4HnK3cay9VEW0R6yPYFHC0AgqhukPzKjq22t5DmAyqGyw=="],

    "flatted": ["flatted@3.3.3", "http://npm.meitu-int.com/flatted/-/flatted-3.3.3.tgz", {}, ""],

    "focus-lock": ["focus-lock@1.3.6", "http://npm.meitu-int.com/focus-lock/-/focus-lock-1.3.6.tgz", { "dependencies": { "tslib": "^2.0.3" } }, ""],

    "follow-redirects": ["follow-redirects@1.15.9", "http://npm.meitu-int.com/follow-redirects/-/follow-redirects-1.15.9.tgz", {}, ""],

    "foreground-child": ["foreground-child@3.3.1", "http://npm.meitu-int.com/foreground-child/-/foreground-child-3.3.1.tgz", { "dependencies": { "cross-spawn": "^7.0.6", "signal-exit": "^4.0.1" } }, ""],

    "form-data": ["form-data@4.0.2", "http://npm.meitu-int.com/form-data/-/form-data-4.0.2.tgz", { "dependencies": { "asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "mime-types": "^2.1.12" } }, ""],

    "forwarded": ["forwarded@0.2.0", "http://npm.meitu-int.com/forwarded/-/forwarded-0.2.0.tgz", {}, ""],

    "fraction.js": ["fraction.js@4.3.7", "http://npm.meitu-int.com/fraction.js/-/fraction.js-4.3.7.tgz", {}, "sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew=="],

    "fresh": ["fresh@2.0.0", "http://npm.meitu-int.com/fresh/-/fresh-2.0.0.tgz", {}, ""],

    "fs-extra": ["fs-extra@11.3.0", "http://npm.meitu-int.com/fs-extra/-/fs-extra-11.3.0.tgz", { "dependencies": { "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0" } }, ""],

    "fs-minipass": ["fs-minipass@2.1.0", "http://npm.meitu-int.com/fs-minipass/-/fs-minipass-2.1.0.tgz", { "dependencies": { "minipass": "^3.0.0" } }, ""],

    "fs.realpath": ["fs.realpath@1.0.0", "http://npm.meitu-int.com/fs.realpath/-/fs.realpath-1.0.0.tgz", {}, ""],

    "fsevents": ["fsevents@2.3.3", "http://npm.meitu-int.com/fsevents/-/fsevents-2.3.3.tgz", { "os": "darwin" }, "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw=="],

    "function-bind": ["function-bind@1.1.2", "http://npm.meitu-int.com/function-bind/-/function-bind-1.1.2.tgz", {}, "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA=="],

    "gauge": ["gauge@3.0.2", "http://npm.meitu-int.com/gauge/-/gauge-3.0.2.tgz", { "dependencies": { "aproba": "^1.0.3 || ^2.0.0", "color-support": "^1.1.2", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.1", "object-assign": "^4.1.1", "signal-exit": "^3.0.0", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "wide-align": "^1.1.2" } }, "sha512-+5J6MS/5XksCuXq++uFRsnUd7Ovu1XenbeuIuNRJxYWjgQbPuFhT14lAvsWfqfAmnwluf1OwMjz39HjfLPci0Q=="],

    "get-intrinsic": ["get-intrinsic@1.3.0", "http://npm.meitu-int.com/get-intrinsic/-/get-intrinsic-1.3.0.tgz", { "dependencies": { "call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0" } }, ""],

    "get-nonce": ["get-nonce@1.0.1", "http://npm.meitu-int.com/get-nonce/-/get-nonce-1.0.1.tgz", {}, "sha512-FJhYRoDaiatfEkUK8HKlicmu/3SGFD51q3itKDGoSTysQJBnfOcxU5GxnhE1E6soB76MbT0MBtnKJuXyAx+96Q=="],

    "get-proto": ["get-proto@1.0.1", "http://npm.meitu-int.com/get-proto/-/get-proto-1.0.1.tgz", { "dependencies": { "dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0" } }, ""],

    "glob": ["glob@10.4.5", "http://npm.meitu-int.com/glob/-/glob-10.4.5.tgz", { "dependencies": { "foreground-child": "^3.1.0", "jackspeak": "^3.1.2", "minimatch": "^9.0.4", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^1.11.1" }, "bin": "dist/esm/bin.mjs" }, "sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg=="],

    "glob-parent": ["glob-parent@6.0.2", "http://npm.meitu-int.com/glob-parent/-/glob-parent-6.0.2.tgz", { "dependencies": { "is-glob": "^4.0.3" } }, ""],

    "globals": ["globals@15.15.0", "http://npm.meitu-int.com/globals/-/globals-15.15.0.tgz", {}, ""],

    "gopd": ["gopd@1.2.0", "http://npm.meitu-int.com/gopd/-/gopd-1.2.0.tgz", {}, ""],

    "graceful-fs": ["graceful-fs@4.2.11", "http://npm.meitu-int.com/graceful-fs/-/graceful-fs-4.2.11.tgz", {}, "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ=="],

    "graphemer": ["graphemer@1.4.0", "http://npm.meitu-int.com/graphemer/-/graphemer-1.4.0.tgz", {}, "sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag=="],

    "has-flag": ["has-flag@4.0.0", "http://npm.meitu-int.com/has-flag/-/has-flag-4.0.0.tgz", {}, ""],

    "has-symbols": ["has-symbols@1.1.0", "http://npm.meitu-int.com/has-symbols/-/has-symbols-1.1.0.tgz", {}, ""],

    "has-tostringtag": ["has-tostringtag@1.0.2", "http://npm.meitu-int.com/has-tostringtag/-/has-tostringtag-1.0.2.tgz", { "dependencies": { "has-symbols": "^1.0.3" } }, ""],

    "has-unicode": ["has-unicode@2.0.1", "http://npm.meitu-int.com/has-unicode/-/has-unicode-2.0.1.tgz", {}, ""],

    "hasown": ["hasown@2.0.2", "http://npm.meitu-int.com/hasown/-/hasown-2.0.2.tgz", { "dependencies": { "function-bind": "^1.1.2" } }, "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ=="],

    "he": ["he@1.2.0", "http://npm.meitu-int.com/he/-/he-1.2.0.tgz", { "bin": "bin/he" }, ""],

    "hotkeys-js": ["hotkeys-js@3.13.10", "http://npm.meitu-int.com/hotkeys-js/-/hotkeys-js-3.13.10.tgz", {}, ""],

    "html-encoding-sniffer": ["html-encoding-sniffer@3.0.0", "http://npm.meitu-int.com/html-encoding-sniffer/-/html-encoding-sniffer-3.0.0.tgz", { "dependencies": { "whatwg-encoding": "^2.0.0" } }, ""],

    "http-errors": ["http-errors@2.0.0", "http://npm.meitu-int.com/http-errors/-/http-errors-2.0.0.tgz", { "dependencies": { "depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1" } }, ""],

    "http-proxy-agent": ["http-proxy-agent@5.0.0", "http://npm.meitu-int.com/http-proxy-agent/-/http-proxy-agent-5.0.0.tgz", { "dependencies": { "@tootallnate/once": "2", "agent-base": "6", "debug": "4" } }, ""],

    "https-proxy-agent": ["https-proxy-agent@5.0.1", "http://npm.meitu-int.com/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz", { "dependencies": { "agent-base": "6", "debug": "4" } }, "sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA=="],

    "i": ["i@0.3.7", "http://npm.meitu-int.com/i/-/i-0.3.7.tgz", {}, ""],

    "iconv-lite": ["iconv-lite@0.6.3", "http://npm.meitu-int.com/iconv-lite/-/iconv-lite-0.6.3.tgz", { "dependencies": { "safer-buffer": ">= 2.1.2 < 3.0.0" } }, ""],

    "ignore": ["ignore@5.3.2", "http://npm.meitu-int.com/ignore/-/ignore-5.3.2.tgz", {}, "sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g=="],

    "import-fresh": ["import-fresh@3.3.1", "http://npm.meitu-int.com/import-fresh/-/import-fresh-3.3.1.tgz", { "dependencies": { "parent-module": "^1.0.0", "resolve-from": "^4.0.0" } }, ""],

    "import-lazy": ["import-lazy@4.0.0", "http://npm.meitu-int.com/import-lazy/-/import-lazy-4.0.0.tgz", {}, ""],

    "imurmurhash": ["imurmurhash@0.1.4", "http://npm.meitu-int.com/imurmurhash/-/imurmurhash-0.1.4.tgz", {}, ""],

    "inflight": ["inflight@1.0.6", "http://npm.meitu-int.com/inflight/-/inflight-1.0.6.tgz", { "dependencies": { "once": "^1.3.0", "wrappy": "1" } }, ""],

    "inherits": ["inherits@2.0.4", "http://npm.meitu-int.com/inherits/-/inherits-2.0.4.tgz", {}, ""],

    "ipaddr.js": ["ipaddr.js@1.9.1", "http://npm.meitu-int.com/ipaddr.js/-/ipaddr.js-1.9.1.tgz", {}, ""],

    "is-arrayish": ["is-arrayish@0.3.2", "http://npm.meitu-int.com/is-arrayish/-/is-arrayish-0.3.2.tgz", {}, ""],

    "is-binary-path": ["is-binary-path@2.1.0", "http://npm.meitu-int.com/is-binary-path/-/is-binary-path-2.1.0.tgz", { "dependencies": { "binary-extensions": "^2.0.0" } }, ""],

    "is-core-module": ["is-core-module@2.16.1", "http://npm.meitu-int.com/is-core-module/-/is-core-module-2.16.1.tgz", { "dependencies": { "hasown": "^2.0.2" } }, "sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w=="],

    "is-extglob": ["is-extglob@2.1.1", "http://npm.meitu-int.com/is-extglob/-/is-extglob-2.1.1.tgz", {}, ""],

    "is-fullwidth-code-point": ["is-fullwidth-code-point@3.0.0", "http://npm.meitu-int.com/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", {}, ""],

    "is-glob": ["is-glob@4.0.3", "http://npm.meitu-int.com/is-glob/-/is-glob-4.0.3.tgz", { "dependencies": { "is-extglob": "^2.1.1" } }, ""],

    "is-in-browser": ["is-in-browser@1.1.3", "http://npm.meitu-int.com/is-in-browser/-/is-in-browser-1.1.3.tgz", {}, ""],

    "is-number": ["is-number@7.0.0", "http://npm.meitu-int.com/is-number/-/is-number-7.0.0.tgz", {}, ""],

    "is-potential-custom-element-name": ["is-potential-custom-element-name@1.0.1", "http://npm.meitu-int.com/is-potential-custom-element-name/-/is-potential-custom-element-name-1.0.1.tgz", {}, ""],

    "is-promise": ["is-promise@4.0.0", "http://npm.meitu-int.com/is-promise/-/is-promise-4.0.0.tgz", {}, ""],

    "isexe": ["isexe@2.0.0", "http://npm.meitu-int.com/isexe/-/isexe-2.0.0.tgz", {}, ""],

    "jackspeak": ["jackspeak@3.4.3", "http://npm.meitu-int.com/jackspeak/-/jackspeak-3.4.3.tgz", { "dependencies": { "@isaacs/cliui": "^8.0.2" }, "optionalDependencies": { "@pkgjs/parseargs": "^0.11.0" } }, "sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw=="],

    "jiti": ["jiti@1.21.7", "http://npm.meitu-int.com/jiti/-/jiti-1.21.7.tgz", { "bin": "bin/jiti.js" }, "sha512-/imKNG4EbWNrVjoNC/1H5/9GFy+tqjGBHCaSsN+P2RnPqjsLmv6UD3Ej+Kj8nBWaRAwyk7kK5ZUc+OEatnTR3A=="],

    "jju": ["jju@1.4.0", "http://npm.meitu-int.com/jju/-/jju-1.4.0.tgz", {}, ""],

    "js-tokens": ["js-tokens@4.0.0", "http://npm.meitu-int.com/js-tokens/-/js-tokens-4.0.0.tgz", {}, ""],

    "js-yaml": ["js-yaml@4.1.0", "http://npm.meitu-int.com/js-yaml/-/js-yaml-4.1.0.tgz", { "dependencies": { "argparse": "^2.0.1" }, "bin": "bin/js-yaml.js" }, ""],

    "jsdom": ["jsdom@20.0.3", "http://npm.meitu-int.com/jsdom/-/jsdom-20.0.3.tgz", { "dependencies": { "abab": "^2.0.6", "acorn": "^8.8.1", "acorn-globals": "^7.0.0", "cssom": "^0.5.0", "cssstyle": "^2.3.0", "data-urls": "^3.0.2", "decimal.js": "^10.4.2", "domexception": "^4.0.0", "escodegen": "^2.0.0", "form-data": "^4.0.0", "html-encoding-sniffer": "^3.0.0", "http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.1", "is-potential-custom-element-name": "^1.0.1", "nwsapi": "^2.2.2", "parse5": "^7.1.1", "saxes": "^6.0.0", "symbol-tree": "^3.2.4", "tough-cookie": "^4.1.2", "w3c-xmlserializer": "^4.0.0", "webidl-conversions": "^7.0.0", "whatwg-encoding": "^2.0.0", "whatwg-mimetype": "^3.0.0", "whatwg-url": "^11.0.0", "ws": "^8.11.0", "xml-name-validator": "^4.0.0" }, "peerDependencies": { "canvas": "^2.5.0" } }, "sha512-SYhBvTh89tTfCD/CRdSOm13mOBa42iTaTyfyEWBdKcGdPxPtLFBXuHR8XHb33YNYaP+lLbmSvBTsnoesCNJEsQ=="],

    "json-buffer": ["json-buffer@3.0.1", "http://npm.meitu-int.com/json-buffer/-/json-buffer-3.0.1.tgz", {}, ""],

    "json-schema-traverse": ["json-schema-traverse@0.4.1", "http://npm.meitu-int.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", {}, ""],

    "json-stable-stringify-without-jsonify": ["json-stable-stringify-without-jsonify@1.0.1", "http://npm.meitu-int.com/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", {}, ""],

    "jsonfile": ["jsonfile@6.1.0", "http://npm.meitu-int.com/jsonfile/-/jsonfile-6.1.0.tgz", { "dependencies": { "universalify": "^2.0.0" }, "optionalDependencies": { "graceful-fs": "^4.1.6" } }, ""],

    "jss": ["jss@10.10.0", "http://npm.meitu-int.com/jss/-/jss-10.10.0.tgz", { "dependencies": { "@babel/runtime": "^7.3.1", "csstype": "^3.0.2", "is-in-browser": "^1.1.3", "tiny-warning": "^1.0.2" } }, "sha512-cqsOTS7jqPsPMjtKYDUpdFC0AbhYFLTcuGRqymgmdJIeQ8cH7+AgX7YSgQy79wXloZq2VvATYxUOUQEvS1V/Zw=="],

    "keyv": ["keyv@4.5.4", "http://npm.meitu-int.com/keyv/-/keyv-4.5.4.tgz", { "dependencies": { "json-buffer": "3.0.1" } }, "sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw=="],

    "kolorist": ["kolorist@1.8.0", "http://npm.meitu-int.com/kolorist/-/kolorist-1.8.0.tgz", {}, "sha512-Y+60/zizpJ3HRH8DCss+q95yr6145JXZo46OTpFvDZWLfRCE4qChOyk1b26nMaNpfHHgxagk9dXT5OP0Tfe+dQ=="],

    "levn": ["levn@0.4.1", "http://npm.meitu-int.com/levn/-/levn-0.4.1.tgz", { "dependencies": { "prelude-ls": "^1.2.1", "type-check": "~0.4.0" } }, ""],

    "lightningcss": ["lightningcss@1.30.1", "http://npm.meitu-int.com/lightningcss/-/lightningcss-1.30.1.tgz", { "dependencies": { "detect-libc": "^2.0.3" }, "optionalDependencies": { "lightningcss-darwin-arm64": "1.30.1", "lightningcss-darwin-x64": "1.30.1", "lightningcss-freebsd-x64": "1.30.1", "lightningcss-linux-arm-gnueabihf": "1.30.1", "lightningcss-linux-arm64-gnu": "1.30.1", "lightningcss-linux-arm64-musl": "1.30.1", "lightningcss-linux-x64-gnu": "1.30.1", "lightningcss-linux-x64-musl": "1.30.1", "lightningcss-win32-arm64-msvc": "1.30.1", "lightningcss-win32-x64-msvc": "1.30.1" } }, "sha512-xi6IyHML+c9+Q3W0S4fCQJOym42pyurFiJUHEcEyHS0CeKzia4yZDEsLlqOFykxOdHpNy0NmvVO31vcSqAxJCg=="],

    "lightningcss-darwin-arm64": ["lightningcss-darwin-arm64@1.30.1", "http://npm.meitu-int.com/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.30.1.tgz", { "os": "darwin", "cpu": "arm64" }, "sha512-c8JK7hyE65X1MHMN+Viq9n11RRC7hgin3HhYKhrMyaXflk5GVplZ60IxyoVtzILeKr+xAJwg6zK6sjTBJ0FKYQ=="],

    "lightningcss-darwin-x64": ["lightningcss-darwin-x64@1.30.1", "http://npm.meitu-int.com/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.30.1.tgz", { "os": "darwin", "cpu": "x64" }, "sha512-k1EvjakfumAQoTfcXUcHQZhSpLlkAuEkdMBsI/ivWw9hL+7FtilQc0Cy3hrx0AAQrVtQAbMI7YjCgYgvn37PzA=="],

    "lightningcss-freebsd-x64": ["lightningcss-freebsd-x64@1.30.1", "http://npm.meitu-int.com/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.30.1.tgz", { "os": "freebsd", "cpu": "x64" }, "sha512-kmW6UGCGg2PcyUE59K5r0kWfKPAVy4SltVeut+umLCFoJ53RdCUWxcRDzO1eTaxf/7Q2H7LTquFHPL5R+Gjyig=="],

    "lightningcss-linux-arm-gnueabihf": ["lightningcss-linux-arm-gnueabihf@1.30.1", "http://npm.meitu-int.com/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.30.1.tgz", { "os": "linux", "cpu": "arm" }, "sha512-MjxUShl1v8pit+6D/zSPq9S9dQ2NPFSQwGvxBCYaBYLPlCWuPh9/t1MRS8iUaR8i+a6w7aps+B4N0S1TYP/R+Q=="],

    "lightningcss-linux-arm64-gnu": ["lightningcss-linux-arm64-gnu@1.30.1", "http://npm.meitu-int.com/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.30.1.tgz", { "os": "linux", "cpu": "arm64" }, "sha512-gB72maP8rmrKsnKYy8XUuXi/4OctJiuQjcuqWNlJQ6jZiWqtPvqFziskH3hnajfvKB27ynbVCucKSm2rkQp4Bw=="],

    "lightningcss-linux-arm64-musl": ["lightningcss-linux-arm64-musl@1.30.1", "http://npm.meitu-int.com/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.30.1.tgz", { "os": "linux", "cpu": "arm64" }, "sha512-jmUQVx4331m6LIX+0wUhBbmMX7TCfjF5FoOH6SD1CttzuYlGNVpA7QnrmLxrsub43ClTINfGSYyHe2HWeLl5CQ=="],

    "lightningcss-linux-x64-gnu": ["lightningcss-linux-x64-gnu@1.30.1", "http://npm.meitu-int.com/lightningcss-linux-x64-gnu/-/lightningcss-linux-x64-gnu-1.30.1.tgz", { "os": "linux", "cpu": "x64" }, "sha512-piWx3z4wN8J8z3+O5kO74+yr6ze/dKmPnI7vLqfSqI8bccaTGY5xiSGVIJBDd5K5BHlvVLpUB3S2YCfelyJ1bw=="],

    "lightningcss-linux-x64-musl": ["lightningcss-linux-x64-musl@1.30.1", "http://npm.meitu-int.com/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.30.1.tgz", { "os": "linux", "cpu": "x64" }, "sha512-rRomAK7eIkL+tHY0YPxbc5Dra2gXlI63HL+v1Pdi1a3sC+tJTcFrHX+E86sulgAXeI7rSzDYhPSeHHjqFhqfeQ=="],

    "lightningcss-win32-arm64-msvc": ["lightningcss-win32-arm64-msvc@1.30.1", "http://npm.meitu-int.com/lightningcss-win32-arm64-msvc/-/lightningcss-win32-arm64-msvc-1.30.1.tgz", { "os": "win32", "cpu": "arm64" }, "sha512-mSL4rqPi4iXq5YVqzSsJgMVFENoa4nGTT/GjO2c0Yl9OuQfPsIfncvLrEW6RbbB24WtZ3xP/2CCmI3tNkNV4oA=="],

    "lightningcss-win32-x64-msvc": ["lightningcss-win32-x64-msvc@1.30.1", "http://npm.meitu-int.com/lightningcss-win32-x64-msvc/-/lightningcss-win32-x64-msvc-1.30.1.tgz", { "os": "win32", "cpu": "x64" }, "sha512-PVqXh48wh4T53F/1CCu8PIPCxLzWyCnn/9T5W1Jpmdy5h9Cwd+0YQS6/LwhHXSafuc61/xg9Lv5OrCby6a++jg=="],

    "lilconfig": ["lilconfig@3.1.3", "http://npm.meitu-int.com/lilconfig/-/lilconfig-3.1.3.tgz", {}, "sha512-/vlFKAoH5Cgt3Ie+JLhRbwOsCQePABiU3tJ1egGvyQ+33R/vcwM2Zl2QR/LzjsBeItPt3oSVXapn+m4nQDvpzw=="],

    "lines-and-columns": ["lines-and-columns@1.2.4", "http://npm.meitu-int.com/lines-and-columns/-/lines-and-columns-1.2.4.tgz", {}, ""],

    "local-pkg": ["local-pkg@1.1.1", "http://npm.meitu-int.com/local-pkg/-/local-pkg-1.1.1.tgz", { "dependencies": { "mlly": "^1.7.4", "pkg-types": "^2.0.1", "quansync": "^0.2.8" } }, ""],

    "locate-path": ["locate-path@6.0.0", "http://npm.meitu-int.com/locate-path/-/locate-path-6.0.0.tgz", { "dependencies": { "p-locate": "^5.0.0" } }, ""],

    "lodash": ["lodash@4.17.21", "http://npm.meitu-int.com/lodash/-/lodash-4.17.21.tgz", {}, ""],

    "lodash-es": ["lodash-es@4.17.21", "http://npm.meitu-int.com/lodash-es/-/lodash-es-4.17.21.tgz", {}, ""],

    "lodash.merge": ["lodash.merge@4.6.2", "http://npm.meitu-int.com/lodash.merge/-/lodash.merge-4.6.2.tgz", {}, ""],

    "loose-envify": ["loose-envify@1.4.0", "http://npm.meitu-int.com/loose-envify/-/loose-envify-1.4.0.tgz", { "dependencies": { "js-tokens": "^3.0.0 || ^4.0.0" }, "bin": "cli.js" }, ""],

    "lottie-web": ["lottie-web@5.12.2", "http://npm.meitu-int.com/lottie-web/-/lottie-web-5.12.2.tgz", {}, "sha512-uvhvYPC8kGPjXT3MyKMrL3JitEAmDMp30lVkuq/590Mw9ok6pWcFCwXJveo0t5uqYw1UREQHofD+jVpdjBv8wg=="],

    "lru-cache": ["lru-cache@6.0.0", "http://npm.meitu-int.com/lru-cache/-/lru-cache-6.0.0.tgz", { "dependencies": { "yallist": "^4.0.0" } }, ""],

    "lucide-react": ["lucide-react@0.469.0", "http://npm.meitu-int.com/lucide-react/-/lucide-react-0.469.0.tgz", { "peerDependencies": { "react": "^16.5.1 || ^17.0.0 || ^18.0.0 || ^19.0.0" } }, "sha512-28vvUnnKQ/dBwiCQtwJw7QauYnE7yd2Cyp4tTTJpvglX4EMpbflcdBgrgToX2j71B3YvugK/NH3BGUk+E/p/Fw=="],

    "magic-string": ["magic-string@0.30.17", "http://npm.meitu-int.com/magic-string/-/magic-string-0.30.17.tgz", { "dependencies": { "@jridgewell/sourcemap-codec": "^1.5.0" } }, "sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA=="],

    "make-dir": ["make-dir@3.1.0", "http://npm.meitu-int.com/make-dir/-/make-dir-3.1.0.tgz", { "dependencies": { "semver": "^6.0.0" } }, ""],

    "math-intrinsics": ["math-intrinsics@1.1.0", "http://npm.meitu-int.com/math-intrinsics/-/math-intrinsics-1.1.0.tgz", {}, ""],

    "media-typer": ["media-typer@1.1.0", "http://npm.meitu-int.com/media-typer/-/media-typer-1.1.0.tgz", {}, ""],

    "merge-descriptors": ["merge-descriptors@2.0.0", "http://npm.meitu-int.com/merge-descriptors/-/merge-descriptors-2.0.0.tgz", {}, ""],

    "merge2": ["merge2@1.4.1", "http://npm.meitu-int.com/merge2/-/merge2-1.4.1.tgz", {}, ""],

    "micromatch": ["micromatch@4.0.8", "http://npm.meitu-int.com/micromatch/-/micromatch-4.0.8.tgz", { "dependencies": { "braces": "^3.0.3", "picomatch": "^2.3.1" } }, "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA=="],

    "mime": ["mime@3.0.0", "http://npm.meitu-int.com/mime/-/mime-3.0.0.tgz", { "bin": "cli.js" }, ""],

    "mime-db": ["mime-db@1.52.0", "http://npm.meitu-int.com/mime-db/-/mime-db-1.52.0.tgz", {}, "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg=="],

    "mime-types": ["mime-types@2.1.35", "http://npm.meitu-int.com/mime-types/-/mime-types-2.1.35.tgz", { "dependencies": { "mime-db": "1.52.0" } }, "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw=="],

    "mimic-response": ["mimic-response@2.1.0", "http://npm.meitu-int.com/mimic-response/-/mimic-response-2.1.0.tgz", {}, ""],

    "minimatch": ["minimatch@3.1.2", "http://npm.meitu-int.com/minimatch/-/minimatch-3.1.2.tgz", { "dependencies": { "brace-expansion": "^1.1.7" } }, "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw=="],

    "minipass": ["minipass@7.1.2", "http://npm.meitu-int.com/minipass/-/minipass-7.1.2.tgz", {}, "sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw=="],

    "minizlib": ["minizlib@2.1.2", "http://npm.meitu-int.com/minizlib/-/minizlib-2.1.2.tgz", { "dependencies": { "minipass": "^3.0.0", "yallist": "^4.0.0" } }, ""],

    "mkdirp": ["mkdirp@1.0.4", "http://npm.meitu-int.com/mkdirp/-/mkdirp-1.0.4.tgz", { "bin": "bin/cmd.js" }, ""],

    "mlly": ["mlly@1.7.4", "http://npm.meitu-int.com/mlly/-/mlly-1.7.4.tgz", { "dependencies": { "acorn": "^8.14.0", "pathe": "^2.0.1", "pkg-types": "^1.3.0", "ufo": "^1.5.4" } }, "sha512-qmdSIPC4bDJXgZTCR7XosJiNKySV7O215tsPtDN9iEO/7q/76b/ijtgRu/+epFXSJhijtTCCGp3DWS549P3xKw=="],

    "ms": ["ms@2.1.3", "http://npm.meitu-int.com/ms/-/ms-2.1.3.tgz", {}, ""],

    "muggle-string": ["muggle-string@0.4.1", "http://npm.meitu-int.com/muggle-string/-/muggle-string-0.4.1.tgz", {}, "sha512-VNTrAak/KhO2i8dqqnqnAHOa3cYBwXEZe9h+D5h/1ZqFSTEFHdM65lR7RoIqq3tBBYavsOXV84NoHXZ0AkPyqQ=="],

    "mz": ["mz@2.7.0", "http://npm.meitu-int.com/mz/-/mz-2.7.0.tgz", { "dependencies": { "any-promise": "^1.0.0", "object-assign": "^4.0.1", "thenify-all": "^1.0.0" } }, ""],

    "nan": ["nan@2.22.2", "http://npm.meitu-int.com/nan/-/nan-2.22.2.tgz", {}, ""],

    "nanoid": ["nanoid@5.1.5", "http://npm.meitu-int.com/nanoid/-/nanoid-5.1.5.tgz", { "bin": "bin/nanoid.js" }, ""],

    "natural-compare": ["natural-compare@1.4.0", "http://npm.meitu-int.com/natural-compare/-/natural-compare-1.4.0.tgz", {}, ""],

    "negotiator": ["negotiator@1.0.0", "http://npm.meitu-int.com/negotiator/-/negotiator-1.0.0.tgz", {}, ""],

    "node-fetch": ["node-fetch@2.7.0", "http://npm.meitu-int.com/node-fetch/-/node-fetch-2.7.0.tgz", { "dependencies": { "whatwg-url": "^5.0.0" }, "peerDependencies": { "encoding": "^0.1.0" }, "optionalPeers": ["encoding"] }, "sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A=="],

    "node-releases": ["node-releases@2.0.19", "http://npm.meitu-int.com/node-releases/-/node-releases-2.0.19.tgz", {}, "sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw=="],

    "nopt": ["nopt@5.0.0", "http://npm.meitu-int.com/nopt/-/nopt-5.0.0.tgz", { "dependencies": { "abbrev": "1" }, "bin": "bin/nopt.js" }, ""],

    "normalize-path": ["normalize-path@3.0.0", "http://npm.meitu-int.com/normalize-path/-/normalize-path-3.0.0.tgz", {}, ""],

    "normalize-range": ["normalize-range@0.1.2", "http://npm.meitu-int.com/normalize-range/-/normalize-range-0.1.2.tgz", {}, ""],

    "npmlog": ["npmlog@5.0.1", "http://npm.meitu-int.com/npmlog/-/npmlog-5.0.1.tgz", { "dependencies": { "are-we-there-yet": "^2.0.0", "console-control-strings": "^1.1.0", "gauge": "^3.0.0", "set-blocking": "^2.0.0" } }, ""],

    "number-precision": ["number-precision@1.6.0", "http://npm.meitu-int.com/number-precision/-/number-precision-1.6.0.tgz", {}, ""],

    "nwsapi": ["nwsapi@2.2.20", "http://npm.meitu-int.com/nwsapi/-/nwsapi-2.2.20.tgz", {}, ""],

    "object-assign": ["object-assign@4.1.1", "http://npm.meitu-int.com/object-assign/-/object-assign-4.1.1.tgz", {}, ""],

    "object-hash": ["object-hash@3.0.0", "http://npm.meitu-int.com/object-hash/-/object-hash-3.0.0.tgz", {}, "sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw=="],

    "object-inspect": ["object-inspect@1.13.4", "http://npm.meitu-int.com/object-inspect/-/object-inspect-1.13.4.tgz", {}, ""],

    "on-finished": ["on-finished@2.4.1", "http://npm.meitu-int.com/on-finished/-/on-finished-2.4.1.tgz", { "dependencies": { "ee-first": "1.1.1" } }, ""],

    "once": ["once@1.4.0", "http://npm.meitu-int.com/once/-/once-1.4.0.tgz", { "dependencies": { "wrappy": "1" } }, ""],

    "optionator": ["optionator@0.9.4", "http://npm.meitu-int.com/optionator/-/optionator-0.9.4.tgz", { "dependencies": { "deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.5" } }, "sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g=="],

    "p-limit": ["p-limit@3.1.0", "http://npm.meitu-int.com/p-limit/-/p-limit-3.1.0.tgz", { "dependencies": { "yocto-queue": "^0.1.0" } }, ""],

    "p-locate": ["p-locate@5.0.0", "http://npm.meitu-int.com/p-locate/-/p-locate-5.0.0.tgz", { "dependencies": { "p-limit": "^3.0.2" } }, ""],

    "package-json-from-dist": ["package-json-from-dist@1.0.1", "http://npm.meitu-int.com/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz", {}, "sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw=="],

    "parent-module": ["parent-module@1.0.1", "http://npm.meitu-int.com/parent-module/-/parent-module-1.0.1.tgz", { "dependencies": { "callsites": "^3.0.0" } }, ""],

    "parse5": ["parse5@7.3.0", "http://npm.meitu-int.com/parse5/-/parse5-7.3.0.tgz", { "dependencies": { "entities": "^6.0.0" } }, ""],

    "parseurl": ["parseurl@1.3.3", "http://npm.meitu-int.com/parseurl/-/parseurl-1.3.3.tgz", {}, ""],

    "path-browserify": ["path-browserify@1.0.1", "http://npm.meitu-int.com/path-browserify/-/path-browserify-1.0.1.tgz", {}, ""],

    "path-exists": ["path-exists@4.0.0", "http://npm.meitu-int.com/path-exists/-/path-exists-4.0.0.tgz", {}, ""],

    "path-is-absolute": ["path-is-absolute@1.0.1", "http://npm.meitu-int.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz", {}, ""],

    "path-key": ["path-key@3.1.1", "http://npm.meitu-int.com/path-key/-/path-key-3.1.1.tgz", {}, ""],

    "path-parse": ["path-parse@1.0.7", "http://npm.meitu-int.com/path-parse/-/path-parse-1.0.7.tgz", {}, ""],

    "path-scurry": ["path-scurry@1.11.1", "http://npm.meitu-int.com/path-scurry/-/path-scurry-1.11.1.tgz", { "dependencies": { "lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0" } }, "sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA=="],

    "path-to-regexp": ["path-to-regexp@8.2.0", "http://npm.meitu-int.com/path-to-regexp/-/path-to-regexp-8.2.0.tgz", {}, ""],

    "pathe": ["pathe@2.0.3", "http://npm.meitu-int.com/pathe/-/pathe-2.0.3.tgz", {}, ""],

    "picocolors": ["picocolors@1.1.1", "http://npm.meitu-int.com/picocolors/-/picocolors-1.1.1.tgz", {}, "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA=="],

    "picomatch": ["picomatch@4.0.2", "http://npm.meitu-int.com/picomatch/-/picomatch-4.0.2.tgz", {}, "sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg=="],

    "pify": ["pify@2.3.0", "http://npm.meitu-int.com/pify/-/pify-2.3.0.tgz", {}, ""],

    "pirates": ["pirates@4.0.7", "http://npm.meitu-int.com/pirates/-/pirates-4.0.7.tgz", {}, ""],

    "pkce-challenge": ["pkce-challenge@5.0.0", "http://npm.meitu-int.com/pkce-challenge/-/pkce-challenge-5.0.0.tgz", {}, ""],

    "pkg-types": ["pkg-types@2.1.0", "http://npm.meitu-int.com/pkg-types/-/pkg-types-2.1.0.tgz", { "dependencies": { "confbox": "^0.2.1", "exsolve": "^1.0.1", "pathe": "^2.0.3" } }, ""],

    "postcss": ["postcss@8.5.3", "http://npm.meitu-int.com/postcss/-/postcss-8.5.3.tgz", { "dependencies": { "nanoid": "^3.3.8", "picocolors": "^1.1.1", "source-map-js": "^1.2.1" } }, ""],

    "postcss-import": ["postcss-import@15.1.0", "http://npm.meitu-int.com/postcss-import/-/postcss-import-15.1.0.tgz", { "dependencies": { "postcss-value-parser": "^4.0.0", "read-cache": "^1.0.0", "resolve": "^1.1.7" }, "peerDependencies": { "postcss": "^8.0.0" } }, "sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew=="],

    "postcss-js": ["postcss-js@4.0.1", "http://npm.meitu-int.com/postcss-js/-/postcss-js-4.0.1.tgz", { "dependencies": { "camelcase-css": "^2.0.1" }, "peerDependencies": { "postcss": "^8.4.21" } }, "sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw=="],

    "postcss-load-config": ["postcss-load-config@4.0.2", "http://npm.meitu-int.com/postcss-load-config/-/postcss-load-config-4.0.2.tgz", { "dependencies": { "lilconfig": "^3.0.0", "yaml": "^2.3.4" }, "peerDependencies": { "postcss": ">=8.0.9", "ts-node": ">=9.0.0" }, "optionalPeers": ["ts-node"] }, "sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ=="],

    "postcss-nested": ["postcss-nested@6.2.0", "http://npm.meitu-int.com/postcss-nested/-/postcss-nested-6.2.0.tgz", { "dependencies": { "postcss-selector-parser": "^6.1.1" }, "peerDependencies": { "postcss": "^8.2.14" } }, "sha512-HQbt28KulC5AJzG+cZtj9kvKB93CFCdLvog1WFLf1D+xmMvPGlBstkpTEZfK5+AN9hfJocyBFCNiqyS48bpgzQ=="],

    "postcss-selector-parser": ["postcss-selector-parser@6.1.2", "http://npm.meitu-int.com/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz", { "dependencies": { "cssesc": "^3.0.0", "util-deprecate": "^1.0.2" } }, "sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg=="],

    "postcss-value-parser": ["postcss-value-parser@4.2.0", "http://npm.meitu-int.com/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", {}, "sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ=="],

    "prelude-ls": ["prelude-ls@1.2.1", "http://npm.meitu-int.com/prelude-ls/-/prelude-ls-1.2.1.tgz", {}, ""],

    "primereact": ["primereact@10.9.5", "http://npm.meitu-int.com/primereact/-/primereact-10.9.5.tgz", { "dependencies": { "@types/react-transition-group": "^4.4.1", "react-transition-group": "^4.4.1" }, "peerDependencies": { "@types/react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^17.0.0 || ^18.0.0 || ^19.0.0" } }, ""],

    "prop-types": ["prop-types@15.8.1", "http://npm.meitu-int.com/prop-types/-/prop-types-15.8.1.tgz", { "dependencies": { "loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1" } }, ""],

    "proxy-addr": ["proxy-addr@2.0.7", "http://npm.meitu-int.com/proxy-addr/-/proxy-addr-2.0.7.tgz", { "dependencies": { "forwarded": "0.2.0", "ipaddr.js": "1.9.1" } }, ""],

    "proxy-from-env": ["proxy-from-env@1.1.0", "http://npm.meitu-int.com/proxy-from-env/-/proxy-from-env-1.1.0.tgz", {}, ""],

    "psl": ["psl@1.15.0", "http://npm.meitu-int.com/psl/-/psl-1.15.0.tgz", { "dependencies": { "punycode": "^2.3.1" } }, "sha512-JZd3gMVBAVQkSs6HdNZo9Sdo0LNcQeMNP3CozBJb3JYC/QUYZTnKxP+f8oWRX4rHP5EurWxqAHTSwUCjlNKa1w=="],

    "punycode": ["punycode@2.3.1", "http://npm.meitu-int.com/punycode/-/punycode-2.3.1.tgz", {}, "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg=="],

    "qiniu-js": ["qiniu-js@2.5.5", "http://npm.meitu-int.com/qiniu-js/-/qiniu-js-2.5.5.tgz", {}, ""],

    "qs": ["qs@6.14.0", "http://npm.meitu-int.com/qs/-/qs-6.14.0.tgz", { "dependencies": { "side-channel": "^1.1.0" } }, ""],

    "quansync": ["quansync@0.2.10", "http://npm.meitu-int.com/quansync/-/quansync-0.2.10.tgz", {}, ""],

    "querystringify": ["querystringify@2.2.0", "http://npm.meitu-int.com/querystringify/-/querystringify-2.2.0.tgz", {}, ""],

    "queue-microtask": ["queue-microtask@1.2.3", "http://npm.meitu-int.com/queue-microtask/-/queue-microtask-1.2.3.tgz", {}, ""],

    "range-parser": ["range-parser@1.2.1", "http://npm.meitu-int.com/range-parser/-/range-parser-1.2.1.tgz", {}, ""],

    "raw-body": ["raw-body@3.0.0", "http://npm.meitu-int.com/raw-body/-/raw-body-3.0.0.tgz", { "dependencies": { "bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.6.3", "unpipe": "1.0.0" } }, ""],

    "react": ["react@18.3.1", "http://npm.meitu-int.com/react/-/react-18.3.1.tgz", { "dependencies": { "loose-envify": "^1.1.0" } }, "sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ=="],

    "react-clientside-effect": ["react-clientside-effect@1.2.7", "http://npm.meitu-int.com/react-clientside-effect/-/react-clientside-effect-1.2.7.tgz", { "dependencies": { "@babel/runtime": "^7.12.13" }, "peerDependencies": { "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc" } }, ""],

    "react-dom": ["react-dom@18.3.1", "http://npm.meitu-int.com/react-dom/-/react-dom-18.3.1.tgz", { "dependencies": { "loose-envify": "^1.1.0", "scheduler": "^0.23.2" }, "peerDependencies": { "react": "^18.3.1" } }, ""],

    "react-focus-lock": ["react-focus-lock@2.13.6", "http://npm.meitu-int.com/react-focus-lock/-/react-focus-lock-2.13.6.tgz", { "dependencies": { "@babel/runtime": "^7.0.0", "focus-lock": "^1.3.6", "prop-types": "^15.6.2", "react-clientside-effect": "^1.2.7", "use-callback-ref": "^1.3.3", "use-sidecar": "^1.1.3" }, "peerDependencies": { "@types/react": "*", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc" } }, ""],

    "react-is": ["react-is@18.3.1", "http://npm.meitu-int.com/react-is/-/react-is-18.3.1.tgz", {}, ""],

    "react-remove-scroll": ["react-remove-scroll@2.6.3", "http://npm.meitu-int.com/react-remove-scroll/-/react-remove-scroll-2.6.3.tgz", { "dependencies": { "react-remove-scroll-bar": "^2.3.7", "react-style-singleton": "^2.2.3", "tslib": "^2.1.0", "use-callback-ref": "^1.3.3", "use-sidecar": "^1.1.3" }, "peerDependencies": { "@types/react": "*", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc" } }, "sha512-pnAi91oOk8g8ABQKGF5/M9qxmmOPxaAnopyTHYfqYEwJhyFrbbBtHuSgtKEoH0jpcxx5o3hXqH1mNd9/Oi+8iQ=="],

    "react-remove-scroll-bar": ["react-remove-scroll-bar@2.3.8", "http://npm.meitu-int.com/react-remove-scroll-bar/-/react-remove-scroll-bar-2.3.8.tgz", { "dependencies": { "react-style-singleton": "^2.2.2", "tslib": "^2.0.0" }, "peerDependencies": { "@types/react": "*", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0" } }, "sha512-9r+yi9+mgU33AKcj6IbT9oRCO78WriSj6t/cF8DWBZJ9aOGPOTEDvdUDz1FwKim7QXWwmHqtdHnRJfhAxEG46Q=="],

    "react-style-singleton": ["react-style-singleton@2.2.3", "http://npm.meitu-int.com/react-style-singleton/-/react-style-singleton-2.2.3.tgz", { "dependencies": { "get-nonce": "^1.0.0", "tslib": "^2.0.0" }, "peerDependencies": { "@types/react": "*", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc" } }, "sha512-b6jSvxvVnyptAiLjbkWLE/lOnR4lfTtDAl+eUC7RZy+QQWc6wRzIV2CE6xBuMmDxc2qIihtDCZD5NPOFl7fRBQ=="],

    "react-transition-group": ["react-transition-group@4.4.5", "http://npm.meitu-int.com/react-transition-group/-/react-transition-group-4.4.5.tgz", { "dependencies": { "@babel/runtime": "^7.5.5", "dom-helpers": "^5.0.1", "loose-envify": "^1.4.0", "prop-types": "^15.6.2" }, "peerDependencies": { "react": ">=16.6.0", "react-dom": ">=16.6.0" } }, ""],

    "read-cache": ["read-cache@1.0.0", "http://npm.meitu-int.com/read-cache/-/read-cache-1.0.0.tgz", { "dependencies": { "pify": "^2.3.0" } }, ""],

    "readable-stream": ["readable-stream@3.6.2", "http://npm.meitu-int.com/readable-stream/-/readable-stream-3.6.2.tgz", { "dependencies": { "inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1" } }, "sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA=="],

    "readdirp": ["readdirp@3.6.0", "http://npm.meitu-int.com/readdirp/-/readdirp-3.6.0.tgz", { "dependencies": { "picomatch": "^2.2.1" } }, ""],

    "require-from-string": ["require-from-string@2.0.2", "http://npm.meitu-int.com/require-from-string/-/require-from-string-2.0.2.tgz", {}, ""],

    "requires-port": ["requires-port@1.0.0", "http://npm.meitu-int.com/requires-port/-/requires-port-1.0.0.tgz", {}, ""],

    "reset.css": ["reset.css@2.0.2", "http://npm.meitu-int.com/reset.css/-/reset.css-2.0.2.tgz", {}, ""],

    "resize-observer-polyfill": ["resize-observer-polyfill@1.5.1", "http://npm.meitu-int.com/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz", {}, ""],

    "resolve": ["resolve@1.22.10", "http://npm.meitu-int.com/resolve/-/resolve-1.22.10.tgz", { "dependencies": { "is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0" }, "bin": "bin/resolve" }, "sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w=="],

    "resolve-from": ["resolve-from@4.0.0", "http://npm.meitu-int.com/resolve-from/-/resolve-from-4.0.0.tgz", {}, ""],

    "reusify": ["reusify@1.1.0", "http://npm.meitu-int.com/reusify/-/reusify-1.1.0.tgz", {}, ""],

    "rimraf": ["rimraf@3.0.2", "http://npm.meitu-int.com/rimraf/-/rimraf-3.0.2.tgz", { "dependencies": { "glob": "^7.1.3" }, "bin": "bin.js" }, ""],

    "rolldown": ["rolldown@1.0.0-beta.29", "http://npm.meitu-int.com/rolldown/-/rolldown-1.0.0-beta.29.tgz", { "dependencies": { "@oxc-project/runtime": "=0.77.3", "@oxc-project/types": "=0.77.3", "@rolldown/pluginutils": "1.0.0-beta.29", "ansis": "^4.0.0" }, "optionalDependencies": { "@rolldown/binding-android-arm64": "1.0.0-beta.29", "@rolldown/binding-darwin-arm64": "1.0.0-beta.29", "@rolldown/binding-darwin-x64": "1.0.0-beta.29", "@rolldown/binding-freebsd-x64": "1.0.0-beta.29", "@rolldown/binding-linux-arm-gnueabihf": "1.0.0-beta.29", "@rolldown/binding-linux-arm64-gnu": "1.0.0-beta.29", "@rolldown/binding-linux-arm64-musl": "1.0.0-beta.29", "@rolldown/binding-linux-arm64-ohos": "1.0.0-beta.29", "@rolldown/binding-linux-x64-gnu": "1.0.0-beta.29", "@rolldown/binding-linux-x64-musl": "1.0.0-beta.29", "@rolldown/binding-wasm32-wasi": "1.0.0-beta.29", "@rolldown/binding-win32-arm64-msvc": "1.0.0-beta.29", "@rolldown/binding-win32-ia32-msvc": "1.0.0-beta.29", "@rolldown/binding-win32-x64-msvc": "1.0.0-beta.29" }, "bin": { "rolldown": "bin/cli.mjs" } }, "sha512-EsoOi8moHN6CAYyTZipxDDVTJn0j2nBCWor4wRU45RQ8ER2qREDykXLr3Ulz6hBh6oBKCFTQIjo21i0FXNo/IA=="],

    "rollup": ["rollup@4.40.2", "http://npm.meitu-int.com/rollup/-/rollup-4.40.2.tgz", { "dependencies": { "@types/estree": "1.0.7" }, "optionalDependencies": { "@rollup/rollup-darwin-arm64": "4.40.2", "fsevents": "~2.3.2" }, "bin": "dist/bin/rollup" }, ""],

    "router": ["router@2.2.0", "http://npm.meitu-int.com/router/-/router-2.2.0.tgz", { "dependencies": { "debug": "^4.4.0", "depd": "^2.0.0", "is-promise": "^4.0.0", "parseurl": "^1.3.3", "path-to-regexp": "^8.0.0" } }, ""],

    "run-parallel": ["run-parallel@1.2.0", "http://npm.meitu-int.com/run-parallel/-/run-parallel-1.2.0.tgz", { "dependencies": { "queue-microtask": "^1.2.2" } }, ""],

    "safe-buffer": ["safe-buffer@5.2.1", "http://npm.meitu-int.com/safe-buffer/-/safe-buffer-5.2.1.tgz", {}, ""],

    "safer-buffer": ["safer-buffer@2.1.2", "http://npm.meitu-int.com/safer-buffer/-/safer-buffer-2.1.2.tgz", {}, ""],

    "saxes": ["saxes@6.0.0", "http://npm.meitu-int.com/saxes/-/saxes-6.0.0.tgz", { "dependencies": { "xmlchars": "^2.2.0" } }, ""],

    "scheduler": ["scheduler@0.23.2", "http://npm.meitu-int.com/scheduler/-/scheduler-0.23.2.tgz", { "dependencies": { "loose-envify": "^1.1.0" } }, "sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ=="],

    "scroll-into-view-if-needed": ["scroll-into-view-if-needed@2.2.31", "http://npm.meitu-int.com/scroll-into-view-if-needed/-/scroll-into-view-if-needed-2.2.31.tgz", { "dependencies": { "compute-scroll-into-view": "^1.0.20" } }, ""],

    "semver": ["semver@7.5.4", "http://npm.meitu-int.com/semver/-/semver-7.5.4.tgz", { "dependencies": { "lru-cache": "^6.0.0" }, "bin": "bin/semver.js" }, "sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA=="],

    "send": ["send@1.2.0", "http://npm.meitu-int.com/send/-/send-1.2.0.tgz", { "dependencies": { "debug": "^4.3.5", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "etag": "^1.8.1", "fresh": "^2.0.0", "http-errors": "^2.0.0", "mime-types": "^3.0.1", "ms": "^2.1.3", "on-finished": "^2.4.1", "range-parser": "^1.2.1", "statuses": "^2.0.1" } }, ""],

    "serve-static": ["serve-static@2.2.0", "http://npm.meitu-int.com/serve-static/-/serve-static-2.2.0.tgz", { "dependencies": { "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "parseurl": "^1.3.3", "send": "^1.2.0" } }, ""],

    "set-blocking": ["set-blocking@2.0.0", "http://npm.meitu-int.com/set-blocking/-/set-blocking-2.0.0.tgz", {}, ""],

    "setprototypeof": ["setprototypeof@1.2.0", "http://npm.meitu-int.com/setprototypeof/-/setprototypeof-1.2.0.tgz", {}, ""],

    "shallowequal": ["shallowequal@1.1.0", "http://npm.meitu-int.com/shallowequal/-/shallowequal-1.1.0.tgz", {}, ""],

    "shebang-command": ["shebang-command@2.0.0", "http://npm.meitu-int.com/shebang-command/-/shebang-command-2.0.0.tgz", { "dependencies": { "shebang-regex": "^3.0.0" } }, ""],

    "shebang-regex": ["shebang-regex@3.0.0", "http://npm.meitu-int.com/shebang-regex/-/shebang-regex-3.0.0.tgz", {}, ""],

    "side-channel": ["side-channel@1.1.0", "http://npm.meitu-int.com/side-channel/-/side-channel-1.1.0.tgz", { "dependencies": { "es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2" } }, ""],

    "side-channel-list": ["side-channel-list@1.0.0", "http://npm.meitu-int.com/side-channel-list/-/side-channel-list-1.0.0.tgz", { "dependencies": { "es-errors": "^1.3.0", "object-inspect": "^1.13.3" } }, ""],

    "side-channel-map": ["side-channel-map@1.0.1", "http://npm.meitu-int.com/side-channel-map/-/side-channel-map-1.0.1.tgz", { "dependencies": { "call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3" } }, ""],

    "side-channel-weakmap": ["side-channel-weakmap@1.0.2", "http://npm.meitu-int.com/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz", { "dependencies": { "call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1" } }, ""],

    "signal-exit": ["signal-exit@4.1.0", "http://npm.meitu-int.com/signal-exit/-/signal-exit-4.1.0.tgz", {}, "sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw=="],

    "simple-concat": ["simple-concat@1.0.1", "http://npm.meitu-int.com/simple-concat/-/simple-concat-1.0.1.tgz", {}, ""],

    "simple-get": ["simple-get@3.1.1", "http://npm.meitu-int.com/simple-get/-/simple-get-3.1.1.tgz", { "dependencies": { "decompress-response": "^4.2.0", "once": "^1.3.1", "simple-concat": "^1.0.0" } }, "sha512-CQ5LTKGfCpvE1K0n2us+kuMPbk/q0EKl82s4aheV9oXjFEz6W/Y7oQFVJuU6QG77hRT4Ghb5RURteF5vnWjupA=="],

    "simple-swizzle": ["simple-swizzle@0.2.2", "http://npm.meitu-int.com/simple-swizzle/-/simple-swizzle-0.2.2.tgz", { "dependencies": { "is-arrayish": "^0.3.1" } }, ""],

    "source-map": ["source-map@0.6.1", "http://npm.meitu-int.com/source-map/-/source-map-0.6.1.tgz", {}, ""],

    "source-map-js": ["source-map-js@1.2.1", "http://npm.meitu-int.com/source-map-js/-/source-map-js-1.2.1.tgz", {}, "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA=="],

    "sprintf-js": ["sprintf-js@1.0.3", "http://npm.meitu-int.com/sprintf-js/-/sprintf-js-1.0.3.tgz", {}, ""],

    "statuses": ["statuses@2.0.1", "http://npm.meitu-int.com/statuses/-/statuses-2.0.1.tgz", {}, ""],

    "string-argv": ["string-argv@0.3.2", "http://npm.meitu-int.com/string-argv/-/string-argv-0.3.2.tgz", {}, "sha512-aqD2Q0144Z+/RqG52NeHEkZauTAUWJO8c6yTftGJKO3Tja5tUgIfmIl6kExvhtxSDP7fXB6DvzkfMpCd/F3G+Q=="],

    "string-width": ["string-width@4.2.3", "http://npm.meitu-int.com/string-width/-/string-width-4.2.3.tgz", { "dependencies": { "emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1" } }, ""],

    "string-width-cjs": ["string-width@4.2.3", "http://npm.meitu-int.com/string-width/-/string-width-4.2.3.tgz", { "dependencies": { "emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1" } }, ""],

    "string_decoder": ["string_decoder@1.3.0", "http://npm.meitu-int.com/string_decoder/-/string_decoder-1.3.0.tgz", { "dependencies": { "safe-buffer": "~5.2.0" } }, ""],

    "strip-ansi": ["strip-ansi@6.0.1", "http://npm.meitu-int.com/strip-ansi/-/strip-ansi-6.0.1.tgz", { "dependencies": { "ansi-regex": "^5.0.1" } }, ""],

    "strip-ansi-cjs": ["strip-ansi@6.0.1", "http://npm.meitu-int.com/strip-ansi/-/strip-ansi-6.0.1.tgz", { "dependencies": { "ansi-regex": "^5.0.1" } }, ""],

    "strip-json-comments": ["strip-json-comments@3.1.1", "http://npm.meitu-int.com/strip-json-comments/-/strip-json-comments-3.1.1.tgz", {}, ""],

    "sucrase": ["sucrase@3.35.0", "http://npm.meitu-int.com/sucrase/-/sucrase-3.35.0.tgz", { "dependencies": { "@jridgewell/gen-mapping": "^0.3.2", "commander": "^4.0.0", "glob": "^10.3.10", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9" }, "bin": { "sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node" } }, "sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA=="],

    "supports-color": ["supports-color@7.2.0", "http://npm.meitu-int.com/supports-color/-/supports-color-7.2.0.tgz", { "dependencies": { "has-flag": "^4.0.0" } }, ""],

    "supports-preserve-symlinks-flag": ["supports-preserve-symlinks-flag@1.0.0", "http://npm.meitu-int.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", {}, "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w=="],

    "svg64": ["svg64@2.0.0", "http://npm.meitu-int.com/svg64/-/svg64-2.0.0.tgz", {}, "sha512-EVgAisxMctUNDSjKGFcx4tkcFrvdqtLIy/MdbBdqcwfpPwsBcwoSKQi+WYoc82c4XWFNVVIwpCup3rpY+M9KJw=="],

    "swr": ["swr@2.3.3", "http://npm.meitu-int.com/swr/-/swr-2.3.3.tgz", { "dependencies": { "dequal": "^2.0.3", "use-sync-external-store": "^1.4.0" }, "peerDependencies": { "react": "^16.11.0 || ^17.0.0 || ^18.0.0 || ^19.0.0" } }, ""],

    "symbol-tree": ["symbol-tree@3.2.4", "http://npm.meitu-int.com/symbol-tree/-/symbol-tree-3.2.4.tgz", {}, ""],

    "tailwind-merge": ["tailwind-merge@2.6.0", "http://npm.meitu-int.com/tailwind-merge/-/tailwind-merge-2.6.0.tgz", {}, "sha512-P+Vu1qXfzediirmHOC3xKGAYeZtPcV9g76X+xg2FD4tYgR71ewMA35Y3sCz3zhiN/dwefRpJX0yBcgwi1fXNQA=="],

    "tailwindcss": ["tailwindcss@3.4.17", "http://npm.meitu-int.com/tailwindcss/-/tailwindcss-3.4.17.tgz", { "dependencies": { "@alloc/quick-lru": "^5.2.0", "arg": "^5.0.2", "chokidar": "^3.6.0", "didyoumean": "^1.2.2", "dlv": "^1.1.3", "fast-glob": "^3.3.2", "glob-parent": "^6.0.2", "is-glob": "^4.0.3", "jiti": "^1.21.6", "lilconfig": "^3.1.3", "micromatch": "^4.0.8", "normalize-path": "^3.0.0", "object-hash": "^3.0.0", "picocolors": "^1.1.1", "postcss": "^8.4.47", "postcss-import": "^15.1.0", "postcss-js": "^4.0.1", "postcss-load-config": "^4.0.2", "postcss-nested": "^6.2.0", "postcss-selector-parser": "^6.1.2", "resolve": "^1.22.8", "sucrase": "^3.35.0" }, "bin": { "tailwind": "lib/cli.js", "tailwindcss": "lib/cli.js" } }, "sha512-w33E2aCvSDP0tW9RZuNXadXlkHXqFzSkQew/aIa2i/Sj8fThxwovwlXHSPXTbAHwEIhBFXAedUhP2tueAKP8Og=="],

    "tailwindcss-animate": ["tailwindcss-animate@1.0.7", "http://npm.meitu-int.com/tailwindcss-animate/-/tailwindcss-animate-1.0.7.tgz", { "peerDependencies": { "tailwindcss": ">=3.0.0 || insiders" } }, "sha512-bl6mpH3T7I3UFxuvDEXLxy/VuFxBk5bbzplh7tXI68mwMokNYd1t9qPBHlnyTwfa4JGC4zP516I1hYYtQ/vspA=="],

    "tar": ["tar@6.2.1", "http://npm.meitu-int.com/tar/-/tar-6.2.1.tgz", { "dependencies": { "chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0" } }, "sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A=="],

    "thenify": ["thenify@3.3.1", "http://npm.meitu-int.com/thenify/-/thenify-3.3.1.tgz", { "dependencies": { "any-promise": "^1.0.0" } }, ""],

    "thenify-all": ["thenify-all@1.6.0", "http://npm.meitu-int.com/thenify-all/-/thenify-all-1.6.0.tgz", { "dependencies": { "thenify": ">= 3.1.0 < 4" } }, ""],

    "tiny-warning": ["tiny-warning@1.0.3", "http://npm.meitu-int.com/tiny-warning/-/tiny-warning-1.0.3.tgz", {}, ""],

    "tinyglobby": ["tinyglobby@0.2.14", "http://npm.meitu-int.com/tinyglobby/-/tinyglobby-0.2.14.tgz", { "dependencies": { "fdir": "^6.4.4", "picomatch": "^4.0.2" } }, "sha512-tX5e7OM1HnYr2+a2C/4V0htOcSQcoSTH9KgJnVvNm5zm/cyEWKJ7j7YutsH9CxMdtOkkLFy2AHrMci9IM8IPZQ=="],

    "to-regex-range": ["to-regex-range@5.0.1", "http://npm.meitu-int.com/to-regex-range/-/to-regex-range-5.0.1.tgz", { "dependencies": { "is-number": "^7.0.0" } }, ""],

    "toidentifier": ["toidentifier@1.0.1", "http://npm.meitu-int.com/toidentifier/-/toidentifier-1.0.1.tgz", {}, ""],

    "tough-cookie": ["tough-cookie@4.1.4", "http://npm.meitu-int.com/tough-cookie/-/tough-cookie-4.1.4.tgz", { "dependencies": { "psl": "^1.1.33", "punycode": "^2.1.1", "universalify": "^0.2.0", "url-parse": "^1.5.3" } }, "sha512-Loo5UUvLD9ScZ6jh8beX1T6sO1w2/MpCRpEP7V280GKMVUQ0Jzar2U3UJPsrdbziLEMMhu3Ujnq//rhiFuIeag=="],

    "tr46": ["tr46@3.0.0", "http://npm.meitu-int.com/tr46/-/tr46-3.0.0.tgz", { "dependencies": { "punycode": "^2.1.1" } }, ""],

    "ts-api-utils": ["ts-api-utils@2.1.0", "http://npm.meitu-int.com/ts-api-utils/-/ts-api-utils-2.1.0.tgz", { "peerDependencies": { "typescript": ">=4.8.4" } }, ""],

    "ts-interface-checker": ["ts-interface-checker@0.1.13", "http://npm.meitu-int.com/ts-interface-checker/-/ts-interface-checker-0.1.13.tgz", {}, ""],

    "tslib": ["tslib@2.8.1", "http://npm.meitu-int.com/tslib/-/tslib-2.8.1.tgz", {}, "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w=="],

    "type-check": ["type-check@0.4.0", "http://npm.meitu-int.com/type-check/-/type-check-0.4.0.tgz", { "dependencies": { "prelude-ls": "^1.2.1" } }, ""],

    "type-is": ["type-is@2.0.1", "http://npm.meitu-int.com/type-is/-/type-is-2.0.1.tgz", { "dependencies": { "content-type": "^1.0.5", "media-typer": "^1.1.0", "mime-types": "^3.0.0" } }, ""],

    "typescript": ["typescript@5.6.3", "http://npm.meitu-int.com/typescript/-/typescript-5.6.3.tgz", { "bin": { "tsc": "bin/tsc", "tsserver": "bin/tsserver" } }, "sha512-hjcS1mhfuyi4WW8IWtjP7brDrG2cuDZukyrYrSauoXGNgx0S7zceP07adYkJycEr56BOUTNPzbInooiN3fn1qw=="],

    "typescript-eslint": ["typescript-eslint@8.32.1", "http://npm.meitu-int.com/typescript-eslint/-/typescript-eslint-8.32.1.tgz", { "dependencies": { "@typescript-eslint/eslint-plugin": "8.32.1", "@typescript-eslint/parser": "8.32.1", "@typescript-eslint/utils": "8.32.1" }, "peerDependencies": { "eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0" } }, ""],

    "ufo": ["ufo@1.6.1", "http://npm.meitu-int.com/ufo/-/ufo-1.6.1.tgz", {}, ""],

    "undici-types": ["undici-types@6.21.0", "http://npm.meitu-int.com/undici-types/-/undici-types-6.21.0.tgz", {}, ""],

    "universalify": ["universalify@0.2.0", "http://npm.meitu-int.com/universalify/-/universalify-0.2.0.tgz", {}, ""],

    "unpipe": ["unpipe@1.0.0", "http://npm.meitu-int.com/unpipe/-/unpipe-1.0.0.tgz", {}, ""],

    "update-browserslist-db": ["update-browserslist-db@1.1.3", "http://npm.meitu-int.com/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", { "dependencies": { "escalade": "^3.2.0", "picocolors": "^1.1.1" }, "peerDependencies": { "browserslist": ">= 4.21.0" }, "bin": "cli.js" }, ""],

    "uri-js": ["uri-js@4.4.1", "http://npm.meitu-int.com/uri-js/-/uri-js-4.4.1.tgz", { "dependencies": { "punycode": "^2.1.0" } }, ""],

    "url-parse": ["url-parse@1.5.10", "http://npm.meitu-int.com/url-parse/-/url-parse-1.5.10.tgz", { "dependencies": { "querystringify": "^2.1.1", "requires-port": "^1.0.0" } }, "sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ=="],

    "use-callback-ref": ["use-callback-ref@1.3.3", "http://npm.meitu-int.com/use-callback-ref/-/use-callback-ref-1.3.3.tgz", { "dependencies": { "tslib": "^2.0.0" }, "peerDependencies": { "@types/react": "*", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc" } }, "sha512-jQL3lRnocaFtu3V00JToYz/4QkNWswxijDaCVNZRiRTO3HQDLsdu1ZtmIUvV4yPp+rvWm5j0y0TG/S61cuijTg=="],

    "use-sidecar": ["use-sidecar@1.1.3", "http://npm.meitu-int.com/use-sidecar/-/use-sidecar-1.1.3.tgz", { "dependencies": { "detect-node-es": "^1.1.0", "tslib": "^2.0.0" }, "peerDependencies": { "@types/react": "*", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc" } }, "sha512-Fedw0aZvkhynoPYlA5WXrMCAMm+nSWdZt6lzJQ7Ok8S6Q+VsHmHpRWndVRJ8Be0ZbkfPc5LRYH+5XrzXcEeLRQ=="],

    "use-sync-external-store": ["use-sync-external-store@1.5.0", "http://npm.meitu-int.com/use-sync-external-store/-/use-sync-external-store-1.5.0.tgz", { "peerDependencies": { "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0" } }, ""],

    "util-deprecate": ["util-deprecate@1.0.2", "http://npm.meitu-int.com/util-deprecate/-/util-deprecate-1.0.2.tgz", {}, ""],

    "vary": ["vary@1.1.2", "http://npm.meitu-int.com/vary/-/vary-1.1.2.tgz", {}, ""],

    "vite": ["rolldown-vite@7.0.10", "http://npm.meitu-int.com/rolldown-vite/-/rolldown-vite-7.0.10.tgz", { "dependencies": { "fdir": "^6.4.6", "lightningcss": "^1.30.1", "picomatch": "^4.0.2", "postcss": "^8.5.6", "rolldown": "1.0.0-beta.29", "tinyglobby": "^0.2.14" }, "optionalDependencies": { "fsevents": "~2.3.3" }, "peerDependencies": { "@types/node": "^20.19.0 || >=22.12.0", "esbuild": "^0.25.0", "jiti": ">=1.21.0", "less": "^4.0.0", "sass": "^1.70.0", "sass-embedded": "^1.70.0", "stylus": ">=0.54.8", "sugarss": "^5.0.0", "terser": "^5.16.0", "tsx": "^4.8.1", "yaml": "^2.4.2" }, "optionalPeers": ["@types/node", "esbuild", "jiti", "less", "sass", "sass-embedded", "stylus", "sugarss", "terser", "tsx", "yaml"], "bin": { "vite": "bin/vite.js" } }, "sha512-t3jMDID78NAJ2PWd0Q5RFrDpD1mFv20ouO/yDbqeHzG2Iyi2ZsjChLKClag1bUm591JJXBsoJIjP6FDkFi9qbw=="],

    "vite-plugin-dts": ["vite-plugin-dts@4.5.3", "http://npm.meitu-int.com/vite-plugin-dts/-/vite-plugin-dts-4.5.3.tgz", { "dependencies": { "@microsoft/api-extractor": "^7.50.1", "@rollup/pluginutils": "^5.1.4", "@volar/typescript": "^2.4.11", "@vue/language-core": "2.2.0", "compare-versions": "^6.1.1", "debug": "^4.4.0", "kolorist": "^1.8.0", "local-pkg": "^1.0.0", "magic-string": "^0.30.17" }, "peerDependencies": { "typescript": "*", "vite": "*" } }, ""],

    "vscode-uri": ["vscode-uri@3.1.0", "http://npm.meitu-int.com/vscode-uri/-/vscode-uri-3.1.0.tgz", {}, ""],

    "w3c-xmlserializer": ["w3c-xmlserializer@4.0.0", "http://npm.meitu-int.com/w3c-xmlserializer/-/w3c-xmlserializer-4.0.0.tgz", { "dependencies": { "xml-name-validator": "^4.0.0" } }, "sha512-d+BFHzbiCx6zGfz0HyQ6Rg69w9k19nviJspaj4yNscGjrHu94sVP+aRm75yEbCh+r2/yR+7q6hux9LVtbuTGBw=="],

    "webidl-conversions": ["webidl-conversions@7.0.0", "http://npm.meitu-int.com/webidl-conversions/-/webidl-conversions-7.0.0.tgz", {}, ""],

    "whatwg-encoding": ["whatwg-encoding@2.0.0", "http://npm.meitu-int.com/whatwg-encoding/-/whatwg-encoding-2.0.0.tgz", { "dependencies": { "iconv-lite": "0.6.3" } }, ""],

    "whatwg-mimetype": ["whatwg-mimetype@3.0.0", "http://npm.meitu-int.com/whatwg-mimetype/-/whatwg-mimetype-3.0.0.tgz", {}, ""],

    "whatwg-url": ["whatwg-url@11.0.0", "http://npm.meitu-int.com/whatwg-url/-/whatwg-url-11.0.0.tgz", { "dependencies": { "tr46": "^3.0.0", "webidl-conversions": "^7.0.0" } }, ""],

    "which": ["which@2.0.2", "http://npm.meitu-int.com/which/-/which-2.0.2.tgz", { "dependencies": { "isexe": "^2.0.0" }, "bin": { "node-which": "bin/node-which" } }, ""],

    "wide-align": ["wide-align@1.1.5", "http://npm.meitu-int.com/wide-align/-/wide-align-1.1.5.tgz", { "dependencies": { "string-width": "^1.0.2 || 2 || 3 || 4" } }, ""],

    "word-wrap": ["word-wrap@1.2.5", "http://npm.meitu-int.com/word-wrap/-/word-wrap-1.2.5.tgz", {}, "sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA=="],

    "workerpool": ["workerpool@9.2.0", "http://npm.meitu-int.com/workerpool/-/workerpool-9.2.0.tgz", {}, "sha512-PKZqBOCo6CYkVOwAxWxQaSF2Fvb5Iv2fCeTP7buyWI2GiynWr46NcXSgK/idoV6e60dgCBfgYc+Un3HMvmqP8w=="],

    "wrap-ansi": ["wrap-ansi@8.1.0", "http://npm.meitu-int.com/wrap-ansi/-/wrap-ansi-8.1.0.tgz", { "dependencies": { "ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1" } }, "sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ=="],

    "wrap-ansi-cjs": ["wrap-ansi@7.0.0", "http://npm.meitu-int.com/wrap-ansi/-/wrap-ansi-7.0.0.tgz", { "dependencies": { "ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0" } }, ""],

    "wrappy": ["wrappy@1.0.2", "http://npm.meitu-int.com/wrappy/-/wrappy-1.0.2.tgz", {}, ""],

    "ws": ["ws@8.18.2", "http://npm.meitu-int.com/ws/-/ws-8.18.2.tgz", { "peerDependencies": { "bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2" }, "optionalPeers": ["bufferutil", "utf-8-validate"] }, ""],

    "xml-name-validator": ["xml-name-validator@4.0.0", "http://npm.meitu-int.com/xml-name-validator/-/xml-name-validator-4.0.0.tgz", {}, ""],

    "xmlchars": ["xmlchars@2.2.0", "http://npm.meitu-int.com/xmlchars/-/xmlchars-2.2.0.tgz", {}, ""],

    "yallist": ["yallist@4.0.0", "http://npm.meitu-int.com/yallist/-/yallist-4.0.0.tgz", {}, ""],

    "yaml": ["yaml@2.7.1", "http://npm.meitu-int.com/yaml/-/yaml-2.7.1.tgz", { "bin": "bin.mjs" }, ""],

    "yocto-queue": ["yocto-queue@0.1.0", "http://npm.meitu-int.com/yocto-queue/-/yocto-queue-0.1.0.tgz", {}, ""],

    "zod": ["zod@3.25.67", "http://npm.meitu-int.com/zod/-/zod-3.25.67.tgz", {}, "sha512-idA2YXwpCdqUSKRCACDE6ItZD9TZzy3OZMtpfLoh6oPR47lipysRrJfjzMqFxQ3uJuUPyUeWe1r9vLH33xO/Qw=="],

    "zod-to-json-schema": ["zod-to-json-schema@3.24.5", "http://npm.meitu-int.com/zod-to-json-schema/-/zod-to-json-schema-3.24.5.tgz", { "peerDependencies": { "zod": "^3.24.1" } }, ""],

    "zustand": ["zustand@5.0.4", "http://npm.meitu-int.com/zustand/-/zustand-5.0.4.tgz", { "peerDependencies": { "@types/react": ">=18.0.0", "immer": ">=9.0.6", "react": ">=18.0.0", "use-sync-external-store": ">=1.2.0" }, "optionalPeers": ["immer"] }, ""],

    "@eslint-community/eslint-utils/eslint-visitor-keys": ["eslint-visitor-keys@3.4.3", "http://npm.meitu-int.com/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz", {}, "sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag=="],

    "@eslint/eslintrc/ajv": ["ajv@6.12.6", "http://npm.meitu-int.com/ajv/-/ajv-6.12.6.tgz", { "dependencies": { "fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2" } }, ""],

    "@eslint/eslintrc/globals": ["globals@14.0.0", "http://npm.meitu-int.com/globals/-/globals-14.0.0.tgz", {}, "sha512-oahGvuMGQlPw/ivIYBjVSrWAfWLBeku5tpPE2fOPLi+WHffIWbuh2tCjhyQhTBPMf5E9jDEH4FOmTYgYwbKwtQ=="],

    "@humanfs/node/@humanwhocodes/retry": ["@humanwhocodes/retry@0.3.1", "http://npm.meitu-int.com/@humanwhocodes%2fretry/-/retry-0.3.1.tgz", {}, "sha512-JBxkERygn7Bv/GbN5Rv8Ul6LVknS+5Bp6RgDC/O8gEBU/yeH5Ui5C/OlWrTb6qct7LjjfT6Re2NxB0ln0yYybA=="],

    "@isaacs/cliui/string-width": ["string-width@5.1.2", "http://npm.meitu-int.com/string-width/-/string-width-5.1.2.tgz", { "dependencies": { "eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1" } }, "sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA=="],

    "@isaacs/cliui/strip-ansi": ["strip-ansi@7.1.0", "http://npm.meitu-int.com/strip-ansi/-/strip-ansi-7.1.0.tgz", { "dependencies": { "ansi-regex": "^6.0.1" } }, "sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ=="],

    "@mapbox/node-pre-gyp/semver": ["semver@7.7.2", "http://npm.meitu-int.com/semver/-/semver-7.7.2.tgz", { "bin": "bin/semver.js" }, ""],

    "@microsoft/api-extractor/minimatch": ["minimatch@3.0.8", "http://npm.meitu-int.com/minimatch/-/minimatch-3.0.8.tgz", { "dependencies": { "brace-expansion": "^1.1.7" } }, "sha512-6FsRAQsxQ61mw+qP1ZzbL9Bc78x2p5OqNgNpnoAFLTrX8n5Kxph0CsnhmKKNXTWjXqU5L0pGPR7hYk+XWZr60Q=="],

    "@microsoft/api-extractor/typescript": ["typescript@5.8.2", "http://npm.meitu-int.com/typescript/-/typescript-5.8.2.tgz", { "bin": { "tsc": "bin/tsc", "tsserver": "bin/tsserver" } }, ""],

    "@microsoft/tsdoc-config/ajv": ["ajv@8.12.0", "http://npm.meitu-int.com/ajv/-/ajv-8.12.0.tgz", { "dependencies": { "fast-deep-equal": "^3.1.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.2.2" } }, "sha512-sRu1kpcO9yLtYxBKvqfTeh9KzZEwO3STyX1HT+4CaDzC6HpTGYhIhPIzj9XuKU7KYDwnaeh5hcOwjy1QuJzBPA=="],

    "@rushstack/node-core-library/ajv": ["ajv@8.13.0", "http://npm.meitu-int.com/ajv/-/ajv-8.13.0.tgz", { "dependencies": { "fast-deep-equal": "^3.1.3", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.4.1" } }, "sha512-PRA911Blj99jR5RMeTunVbNXMF6Lp4vZXnk5GQjcnUWUTsrXtekg/pnmFFI2u/I36Y/2bITGS30GZCXei6uNkA=="],

    "@rushstack/terminal/supports-color": ["supports-color@8.1.1", "http://npm.meitu-int.com/supports-color/-/supports-color-8.1.1.tgz", { "dependencies": { "has-flag": "^4.0.0" } }, ""],

    "@rushstack/ts-command-line/argparse": ["argparse@1.0.10", "http://npm.meitu-int.com/argparse/-/argparse-1.0.10.tgz", { "dependencies": { "sprintf-js": "~1.0.2" } }, ""],

    "@typescript-eslint/eslint-plugin/ignore": ["ignore@7.0.4", "http://npm.meitu-int.com/ignore/-/ignore-7.0.4.tgz", {}, ""],

    "@typescript-eslint/typescript-estree/minimatch": ["minimatch@9.0.5", "http://npm.meitu-int.com/minimatch/-/minimatch-9.0.5.tgz", { "dependencies": { "brace-expansion": "^2.0.1" } }, "sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow=="],

    "@typescript-eslint/typescript-estree/semver": ["semver@7.7.2", "http://npm.meitu-int.com/semver/-/semver-7.7.2.tgz", { "bin": "bin/semver.js" }, ""],

    "@vue/compiler-core/entities": ["entities@4.5.0", "http://npm.meitu-int.com/entities/-/entities-4.5.0.tgz", {}, "sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw=="],

    "@vue/language-core/minimatch": ["minimatch@9.0.5", "http://npm.meitu-int.com/minimatch/-/minimatch-9.0.5.tgz", { "dependencies": { "brace-expansion": "^2.0.1" } }, "sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow=="],

    "accepts/mime-types": ["mime-types@3.0.1", "http://npm.meitu-int.com/mime-types/-/mime-types-3.0.1.tgz", { "dependencies": { "mime-db": "^1.54.0" } }, ""],

    "ajv-draft-04/ajv": ["ajv@8.13.0", "http://npm.meitu-int.com/ajv/-/ajv-8.13.0.tgz", { "dependencies": { "fast-deep-equal": "^3.1.3", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.4.1" } }, "sha512-PRA911Blj99jR5RMeTunVbNXMF6Lp4vZXnk5GQjcnUWUTsrXtekg/pnmFFI2u/I36Y/2bITGS30GZCXei6uNkA=="],

    "ajv-formats/ajv": ["ajv@8.13.0", "http://npm.meitu-int.com/ajv/-/ajv-8.13.0.tgz", { "dependencies": { "fast-deep-equal": "^3.1.3", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.4.1" } }, "sha512-PRA911Blj99jR5RMeTunVbNXMF6Lp4vZXnk5GQjcnUWUTsrXtekg/pnmFFI2u/I36Y/2bITGS30GZCXei6uNkA=="],

    "anymatch/picomatch": ["picomatch@2.3.1", "http://npm.meitu-int.com/picomatch/-/picomatch-2.3.1.tgz", {}, "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="],

    "chokidar/glob-parent": ["glob-parent@5.1.2", "http://npm.meitu-int.com/glob-parent/-/glob-parent-5.1.2.tgz", { "dependencies": { "is-glob": "^4.0.1" } }, ""],

    "color/color-convert": ["color-convert@1.9.3", "http://npm.meitu-int.com/color-convert/-/color-convert-1.9.3.tgz", { "dependencies": { "color-name": "1.1.3" } }, ""],

    "cssstyle/cssom": ["cssom@0.3.8", "http://npm.meitu-int.com/cssom/-/cssom-0.3.8.tgz", {}, ""],

    "express/mime-types": ["mime-types@3.0.1", "http://npm.meitu-int.com/mime-types/-/mime-types-3.0.1.tgz", { "dependencies": { "mime-db": "^1.54.0" } }, ""],

    "fast-glob/glob-parent": ["glob-parent@5.1.2", "http://npm.meitu-int.com/glob-parent/-/glob-parent-5.1.2.tgz", { "dependencies": { "is-glob": "^4.0.1" } }, ""],

    "fs-extra/universalify": ["universalify@2.0.1", "http://npm.meitu-int.com/universalify/-/universalify-2.0.1.tgz", {}, ""],

    "fs-minipass/minipass": ["minipass@3.3.6", "http://npm.meitu-int.com/minipass/-/minipass-3.3.6.tgz", { "dependencies": { "yallist": "^4.0.0" } }, "sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw=="],

    "gauge/signal-exit": ["signal-exit@3.0.7", "http://npm.meitu-int.com/signal-exit/-/signal-exit-3.0.7.tgz", {}, "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ=="],

    "glob/minimatch": ["minimatch@9.0.5", "http://npm.meitu-int.com/minimatch/-/minimatch-9.0.5.tgz", { "dependencies": { "brace-expansion": "^2.0.1" } }, "sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow=="],

    "jsonfile/universalify": ["universalify@2.0.1", "http://npm.meitu-int.com/universalify/-/universalify-2.0.1.tgz", {}, ""],

    "make-dir/semver": ["semver@6.3.1", "http://npm.meitu-int.com/semver/-/semver-6.3.1.tgz", { "bin": "bin/semver.js" }, "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA=="],

    "micromatch/picomatch": ["picomatch@2.3.1", "http://npm.meitu-int.com/picomatch/-/picomatch-2.3.1.tgz", {}, "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="],

    "minizlib/minipass": ["minipass@3.3.6", "http://npm.meitu-int.com/minipass/-/minipass-3.3.6.tgz", { "dependencies": { "yallist": "^4.0.0" } }, "sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw=="],

    "mlly/pkg-types": ["pkg-types@1.3.1", "http://npm.meitu-int.com/pkg-types/-/pkg-types-1.3.1.tgz", { "dependencies": { "confbox": "^0.1.8", "mlly": "^1.7.4", "pathe": "^2.0.1" } }, "sha512-/Jm5M4RvtBFVkKWRu2BLUTNP8/M2a+UwuAX+ae4770q1qVGtfjG+WTCupoZixokjmHiry8uI+dlY8KXYV5HVVQ=="],

    "node-fetch/whatwg-url": ["whatwg-url@5.0.0", "http://npm.meitu-int.com/whatwg-url/-/whatwg-url-5.0.0.tgz", { "dependencies": { "tr46": "~0.0.3", "webidl-conversions": "^3.0.0" } }, ""],

    "path-scurry/lru-cache": ["lru-cache@10.4.3", "http://npm.meitu-int.com/lru-cache/-/lru-cache-10.4.3.tgz", {}, "sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ=="],

    "path-scurry/minipass": ["minipass@7.1.2", "http://npm.meitu-int.com/minipass/-/minipass-7.1.2.tgz", {}, "sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw=="],

    "postcss/nanoid": ["nanoid@3.3.11", "http://npm.meitu-int.com/nanoid/-/nanoid-3.3.11.tgz", { "bin": "bin/nanoid.cjs" }, ""],

    "prop-types/react-is": ["react-is@16.13.1", "http://npm.meitu-int.com/react-is/-/react-is-16.13.1.tgz", {}, ""],

    "readdirp/picomatch": ["picomatch@2.3.1", "http://npm.meitu-int.com/picomatch/-/picomatch-2.3.1.tgz", {}, "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="],

    "rimraf/glob": ["glob@7.2.3", "http://npm.meitu-int.com/glob/-/glob-7.2.3.tgz", { "dependencies": { "fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0" } }, "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q=="],

    "send/mime-types": ["mime-types@3.0.1", "http://npm.meitu-int.com/mime-types/-/mime-types-3.0.1.tgz", { "dependencies": { "mime-db": "^1.54.0" } }, ""],

    "tailwindcss/glob-parent": ["glob-parent@6.0.2", "http://npm.meitu-int.com/glob-parent/-/glob-parent-6.0.2.tgz", { "dependencies": { "is-glob": "^4.0.3" } }, ""],

    "tar/minipass": ["minipass@5.0.0", "http://npm.meitu-int.com/minipass/-/minipass-5.0.0.tgz", {}, "sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ=="],

    "type-is/mime-types": ["mime-types@3.0.1", "http://npm.meitu-int.com/mime-types/-/mime-types-3.0.1.tgz", { "dependencies": { "mime-db": "^1.54.0" } }, ""],

    "vite/postcss": ["postcss@8.5.6", "http://npm.meitu-int.com/postcss/-/postcss-8.5.6.tgz", { "dependencies": { "nanoid": "^3.3.11", "picocolors": "^1.1.1", "source-map-js": "^1.2.1" } }, "sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg=="],

    "wrap-ansi/ansi-styles": ["ansi-styles@6.2.1", "http://npm.meitu-int.com/ansi-styles/-/ansi-styles-6.2.1.tgz", {}, "sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug=="],

    "wrap-ansi/string-width": ["string-width@5.1.2", "http://npm.meitu-int.com/string-width/-/string-width-5.1.2.tgz", { "dependencies": { "eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1" } }, "sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA=="],

    "wrap-ansi/strip-ansi": ["strip-ansi@7.1.0", "http://npm.meitu-int.com/strip-ansi/-/strip-ansi-7.1.0.tgz", { "dependencies": { "ansi-regex": "^6.0.1" } }, "sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ=="],

    "@eslint/eslintrc/ajv/json-schema-traverse": ["json-schema-traverse@0.4.1", "http://npm.meitu-int.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", {}, ""],

    "@isaacs/cliui/string-width/emoji-regex": ["emoji-regex@9.2.2", "http://npm.meitu-int.com/emoji-regex/-/emoji-regex-9.2.2.tgz", {}, ""],

    "@isaacs/cliui/strip-ansi/ansi-regex": ["ansi-regex@6.1.0", "http://npm.meitu-int.com/ansi-regex/-/ansi-regex-6.1.0.tgz", {}, "sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA=="],

    "@microsoft/tsdoc-config/ajv/json-schema-traverse": ["json-schema-traverse@1.0.0", "http://npm.meitu-int.com/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", {}, ""],

    "@rushstack/node-core-library/ajv/json-schema-traverse": ["json-schema-traverse@1.0.0", "http://npm.meitu-int.com/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", {}, ""],

    "@typescript-eslint/typescript-estree/minimatch/brace-expansion": ["brace-expansion@2.0.1", "http://npm.meitu-int.com/brace-expansion/-/brace-expansion-2.0.1.tgz", { "dependencies": { "balanced-match": "^1.0.0" } }, ""],

    "@vue/language-core/minimatch/brace-expansion": ["brace-expansion@2.0.1", "http://npm.meitu-int.com/brace-expansion/-/brace-expansion-2.0.1.tgz", { "dependencies": { "balanced-match": "^1.0.0" } }, ""],

    "accepts/mime-types/mime-db": ["mime-db@1.54.0", "http://npm.meitu-int.com/mime-db/-/mime-db-1.54.0.tgz", {}, ""],

    "ajv-draft-04/ajv/json-schema-traverse": ["json-schema-traverse@1.0.0", "http://npm.meitu-int.com/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", {}, ""],

    "ajv-formats/ajv/json-schema-traverse": ["json-schema-traverse@1.0.0", "http://npm.meitu-int.com/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", {}, ""],

    "color/color-convert/color-name": ["color-name@1.1.3", "http://npm.meitu-int.com/color-name/-/color-name-1.1.3.tgz", {}, ""],

    "express/mime-types/mime-db": ["mime-db@1.54.0", "http://npm.meitu-int.com/mime-db/-/mime-db-1.54.0.tgz", {}, ""],

    "glob/minimatch/brace-expansion": ["brace-expansion@2.0.1", "http://npm.meitu-int.com/brace-expansion/-/brace-expansion-2.0.1.tgz", { "dependencies": { "balanced-match": "^1.0.0" } }, ""],

    "mlly/pkg-types/confbox": ["confbox@0.1.8", "http://npm.meitu-int.com/confbox/-/confbox-0.1.8.tgz", {}, "sha512-RMtmw0iFkeR4YV+fUOSucriAQNb9g8zFR52MWCtl+cCZOFRNL6zeB395vPzFhEjjn4fMxXudmELnl/KF/WrK6w=="],

    "node-fetch/whatwg-url/tr46": ["tr46@0.0.3", "http://npm.meitu-int.com/tr46/-/tr46-0.0.3.tgz", {}, ""],

    "node-fetch/whatwg-url/webidl-conversions": ["webidl-conversions@3.0.1", "http://npm.meitu-int.com/webidl-conversions/-/webidl-conversions-3.0.1.tgz", {}, ""],

    "send/mime-types/mime-db": ["mime-db@1.54.0", "http://npm.meitu-int.com/mime-db/-/mime-db-1.54.0.tgz", {}, ""],

    "type-is/mime-types/mime-db": ["mime-db@1.54.0", "http://npm.meitu-int.com/mime-db/-/mime-db-1.54.0.tgz", {}, ""],

    "vite/postcss/nanoid": ["nanoid@3.3.11", "http://npm.meitu-int.com/nanoid/-/nanoid-3.3.11.tgz", { "bin": "bin/nanoid.cjs" }, ""],

    "wrap-ansi/string-width/emoji-regex": ["emoji-regex@9.2.2", "http://npm.meitu-int.com/emoji-regex/-/emoji-regex-9.2.2.tgz", {}, ""],

    "wrap-ansi/strip-ansi/ansi-regex": ["ansi-regex@6.1.0", "http://npm.meitu-int.com/ansi-regex/-/ansi-regex-6.1.0.tgz", {}, "sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA=="],
  }
}
