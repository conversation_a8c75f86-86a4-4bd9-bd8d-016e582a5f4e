# RenderExtend 插件图层顺序修复

## 修复内容
修复了 RenderExtend 插件启用期间插入新图形时，target 和 container 无法保持在最顶层的问题。

## 修改的文件
- `src/render/plugins/renderExtend/plugin.ts`

## 修改内容

### 1. 修复事件监听器绑定
**修改前：**
```typescript
if (this.options.renderMask) {
    // ... 其他代码
    render._FC.on('object:added', this.objectAddedHandler)
}
```

**修改后：**
```typescript
// 始终监听 object:added 事件，确保 target 和 container 在最顶层
render._FC.on('object:added', this.objectAddedHandler)

if (this.options.renderMask) {
    // ... 其他代码
}
```

### 2. 改进 objectAddedHandler 逻辑
**修改前：**
```typescript
objectAddedHandler = () => {
    console.log('object:added, +++++++++++++++++++++++++++++objectAddedHandler', this.options.renderMask)
    if (this.options.renderMask) {
        this.render._FC.bringObjectToFront(this._extendContainer)
        this.render._FC.bringObjectToFront(this._extendTarget)
    }
    this.render._FC.renderAll()
}
```

**修改后：**
```typescript
objectAddedHandler = () => {
    console.log('object:added, +++++++++++++++++++++++++++++objectAddedHandler', this.options.renderMask)
    // 确保 target 和 container 始终在最顶层，无论是否有 renderMask
    this.render._FC.bringObjectToFront(this._extendContainer)
    this.render._FC.bringObjectToFront(this._extendTarget)
    
    // 如果有遮罩，也要确保遮罩在合适的位置
    if (this.options.renderMask && this.maskElement) {
        // 遮罩应该在 container 和 target 之下，但在其他元素之上
        this.render._FC.bringObjectToFront(this.maskElement)
        this.render._FC.bringObjectToFront(this._extendContainer)
        this.render._FC.bringObjectToFront(this._extendTarget)
    }
    
    this.render._FC.renderAll()
}
```

### 3. 清理重复的事件移除
在 `__destroy__` 方法中移除了重复的 `object:added` 事件移除代码，因为 `unBindEvent` 方法已经处理了这个事件的移除。

## 修复前的问题
1. 只有在 `renderMask: true` 时才会监听 `object:added` 事件
2. 当 `renderMask: false` 时，插入新图形后 target 和 container 可能被覆盖
3. 遮罩的图层顺序可能不正确

## 修复后的效果
1. ✅ 无论是否启用 renderMask，都会监听 `object:added` 事件
2. ✅ 插入新图形时，target 和 container 始终保持在最顶层
3. ✅ 当有遮罩时，图层顺序为：其他元素 < 遮罩 < container < target
4. ✅ 正确清理事件监听器，避免内存泄漏

## 测试建议
1. 启用 RenderExtend 插件（renderMask: false）
2. 插入新的图形元素
3. 验证 target 和 container 是否仍然在最顶层
4. 启用 RenderExtend 插件（renderMask: true）
5. 插入新的图形元素
6. 验证图层顺序：其他元素 < 遮罩 < container < target
